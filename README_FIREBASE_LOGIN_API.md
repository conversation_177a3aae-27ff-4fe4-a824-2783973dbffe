# Firebase Login Status API Implementation

## نظرة عامة
تم تنفيذ API للتحكم في إظهار/إخفاء زر تسجيل الدخول مع Google من الداش بورد باستخدام نفس الممارسات المتبعة في التطبيق.

## الملفات المضافة

### 1. Model
- `lib/data/models/auth_models/firebase_login_status_models/firebase_login_status_models.dart`
- `lib/data/models/auth_models/firebase_login_status_models/firebase_login_status_models.g.dart`

### 2. Repository
- `lib/data/repo/auth_repo/firebase_login_status_repo.dart`

### 3. Cubit & States
- `lib/bloc/firebase_login_status_cubit/firebase_login_status_cubit.dart`
- `lib/bloc/firebase_login_status_cubit/firebase_login_status_states.dart`

### 4. Configuration
- تم إضافة endpoint جديد في `lib/config/config_base.dart`:
  ```dart
  static const String firebaseLoginStatus = "auth/firebase-login-status";
  ```

### 5. Integration
- تم إضافة `FirebaseLoginStatusCubit` إلى `MultiBlocProvider` في `main.dart`
- تم تعديل `_buildGoogleButton()` في `login_screen.dart` لاستخدام الـ API

## كيفية عمل الـ API

### Endpoint
```
POST {{stage}}/api/school/auth/firebase-login-status
```

### Request Body
```json
{
  "idToken": "firebase_id_token_here",
  "accessToken": "firebase_access_token_here", // اختياري
  "firebase_token": "fcm_token_here"
}
```

### Response
```json
{
  "data": 1,
  "message": "Parent is logged in",
  "status": true
}
```

أو

```json
{
  "data": 0,
  "message": "Parent is not logged in",
  "status": true
}
```

## المنطق المطبق

### في صفحة تسجيل الدخول:
1. عند تحميل الصفحة، يتم استدعاء `checkFirebaseLoginAvailability()`
2. إذا كانت النتيجة `data: 0` → يتم إظهار زر Google Sign-In
3. إذا كانت النتيجة `data: 1` → يتم إخفاء زر Google Sign-In
4. في حالة الخطأ → يتم إخفاء الزر

### في الداش بورد:
- يمكن للمدير التحكم في قيمة الـ response من الـ backend
- `data: 1` = إخفاء زر Google Sign-In
- `data: 0` = إظهار زر Google Sign-In

## الاستخدام

### في أي مكان في التطبيق:
```dart
// للتحقق من حالة Firebase login
context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();

// للتحقق مع token محدد
context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginStatus(
  idToken: "firebase_id_token",
  accessToken: "firebase_access_token",
);
```

### مراقبة الحالة:
```dart
BlocBuilder<FirebaseLoginStatusCubit, FirebaseLoginStatusState>(
  builder: (context, state) {
    if (state.isLoading) {
      return CircularProgressIndicator();
    }
    
    if (state.hasLoginStatusData && state.isParentNotLoggedIn) {
      // إظهار زر Google Sign-In
      return GoogleSignInButton();
    }
    
    // إخفاء الزر
    return SizedBox.shrink();
  },
)
```

## المميزات

1. **اتباع نفس الممارسات**: استخدام نفس pattern المتبع في التطبيق
2. **عدم تكرار الكود**: استخدام MultiBlocProvider الموجود
3. **مرونة في التحكم**: يمكن التحكم من الداش بورد
4. **معالجة الأخطاء**: معالجة شاملة للأخطاء
5. **سهولة الاستخدام**: API بسيط وواضح

## ملاحظات مهمة

- الـ API يعمل مع Firebase ID tokens للتحقق من صحة المستخدم
- يتم إرسال FCM token مع كل طلب للإشعارات
- الـ response يتبع نفس structure المستخدم في باقي APIs
- يمكن استخدام الـ API من أي frontend (Flutter, React, Vue, etc.)

## مثال للاختبار
راجع `lib/examples/firebase_login_api_usage.dart` لمثال كامل على الاستخدام.
