<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Busaty - School</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>bus</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Busaty needs your location to show nearby schools and educational institutions on the map, helping you find and navigate to schools in your area. For example, we&apos;ll display the distance to each school and provide turn-by-turn directions.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Busaty uses your location in the background to send you notifications about nearby school events and schedule changes. For example, you&apos;ll receive alerts about upcoming parent-teacher meetings at schools you follow when you&apos;re in the vicinity.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Busaty needs access to your photo library to let you share school-related photos and documents. For example, you can upload class pictures for school events, share student achievements, or submit signed permission slips and assignments through the app.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Busaty uses your location to show nearby schools, provide navigation, and send relevant notifications about school events. For example, we&apos;ll help you find the closest schools, provide directions, and notify you about important events at schools you follow when you&apos;re nearby.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Busaty needs access to save photos to your photo library.</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>REVERSED_CLIENT_ID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.545165014521-6lga42ejvda9e36l607gkllobevvpjfn</string>
			</array>
		</dict>
	</array>
</dict>
</plist>
