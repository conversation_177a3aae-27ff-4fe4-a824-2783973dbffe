import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_states.dart';
import 'package:bus/data/models/auth_models/firebase_login_status_models/firebase_login_status_models.dart';
import 'package:bus/data/repo/auth_repo/firebase_login_status_repo.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

/// Cubit for managing Firebase login status
/// Follows the same pattern as other cubit classes in the app
class FirebaseLoginStatusCubit extends Cubit<FirebaseLoginStatusState> {
  final _firebaseLoginStatusRepo = FirebaseLoginStatusRepo();
  
  FirebaseLoginStatusCubit() : super(const FirebaseLoginStatusState());

  /// Checks Firebase login status using Firebase ID token
  /// 
  /// This method is used to verify if a parent is currently logged in
  /// with their Firebase account by validating their ID token
  /// 
  /// Parameters:
  /// - [idToken]: Firebase ID token from client authentication
  /// - [accessToken]: Firebase access token (optional)
  Future<void> checkFirebaseLoginStatus({
    String? idToken,
    String? accessToken,
  }) async {
    try {
      // Emit loading state
      emit(state.copyWith(rStates: ResponseState.loading));
      
      debugPrint("FirebaseLoginStatusCubit: Checking Firebase login status");

      // Call repository to check login status
      DataState<FirebaseLoginStatusModels> response = 
          await _firebaseLoginStatusRepo.checkFirebaseLoginStatus(
        idToken: idToken,
        accessToken: accessToken,
      );

      // Handle response
      if (response is DataSuccess) {
        debugPrint("FirebaseLoginStatusCubit: Login status check successful");
        debugPrint("Login status data: ${response.data}");
        
        emit(
          state.copyWith(
            firebaseLoginStatusModels: response.data,
            rStates: ResponseState.success,
            message: response.data?.message ?? "Login status retrieved successfully",
          ),
        );
      } else if (response is DataFailed) {
        debugPrint("FirebaseLoginStatusCubit: Login status check failed");
        debugPrint("Error message: ${response.message}");
        
        emit(
          state.copyWith(
            rStates: ResponseState.failure,
            message: response.message ?? "Failed to check login status",
          ),
        );
      }
    } catch (e) {
      debugPrint("FirebaseLoginStatusCubit: Exception occurred - $e");
      Logger().e("Firebase login status cubit error: $e");
      
      emit(
        state.copyWith(
          rStates: ResponseState.failure,
          message: "An unexpected error occurred while checking login status",
        ),
      );
    }
  }

  /// Checks Firebase login availability without requiring tokens
  /// 
  /// This method can be used to check if Firebase login is generally
  /// available or enabled on the dashboard
  Future<void> checkFirebaseLoginAvailability() async {
    try {
      // Emit loading state
      emit(state.copyWith(rStates: ResponseState.loading));
      
      debugPrint("FirebaseLoginStatusCubit: Checking Firebase login availability");

      // Call repository to check login availability
      DataState<FirebaseLoginStatusModels> response = 
          await _firebaseLoginStatusRepo.checkFirebaseLoginAvailability();

      // Handle response
      if (response is DataSuccess) {
        debugPrint("FirebaseLoginStatusCubit: Login availability check successful");
        debugPrint("Login availability data: ${response.data}");
        
        emit(
          state.copyWith(
            firebaseLoginStatusModels: response.data,
            rStates: ResponseState.success,
            message: response.data?.message ?? "Login availability retrieved successfully",
          ),
        );
      } else if (response is DataFailed) {
        debugPrint("FirebaseLoginStatusCubit: Login availability check failed");
        debugPrint("Error message: ${response.message}");
        
        emit(
          state.copyWith(
            rStates: ResponseState.failure,
            message: response.message ?? "Failed to check login availability",
          ),
        );
      }
    } catch (e) {
      debugPrint("FirebaseLoginStatusCubit: Exception occurred - $e");
      Logger().e("Firebase login availability cubit error: $e");
      
      emit(
        state.copyWith(
          rStates: ResponseState.failure,
          message: "An unexpected error occurred while checking login availability",
        ),
      );
    }
  }

  /// Resets the cubit state to initial state
  void resetState() {
    debugPrint("FirebaseLoginStatusCubit: Resetting state");
    emit(const FirebaseLoginStatusState());
  }

  /// Helper method to get current login status
  bool get isParentLoggedIn => state.isParentLoggedIn;

  /// Helper method to check if parent is not logged in
  bool get isParentNotLoggedIn => state.isParentNotLoggedIn;

  /// Helper method to check if we have valid data
  bool get hasValidData => state.hasLoginStatusData && state.isSuccess;
}
