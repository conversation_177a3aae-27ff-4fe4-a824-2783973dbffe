import 'package:flutter/foundation.dart';

/// Configuration class for Google Sign-In Client IDs
/// All Client IDs are from test-5c820 Firebase project
///
/// IMPORTANT: This file is for reference only.
/// The actual Google Sign-In now uses google-services.json automatically.
/// No manual Client ID configuration is needed.
class GoogleSignInConfig {
  // SHA-1 Fingerprints for reference (from google-services.json)
  static const String debugSHA1 =
      '8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23';
  static const String releaseSHA1 =
      'A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E';
  static const String playStoreSHA1 =
      '3C:ED:A5:0A:3B:31:26:2B:0D:C1:AF:26:B8:5E:C2:84:DA:66:B4:67'; // From Google Play Console - VERIFIED

  // Client IDs from google-services.json for com.busaty.school
  static const String androidDebugClientId =
      '545165014521-ujhsbr089jkoo83mgb076oqig53hbvu8.apps.googleusercontent.com';
  static const String androidReleaseClientId =
      '545165014521-hg4k13r1ki88kckkhp53ko7mhv247qeh.apps.googleusercontent.com';
  static const String androidPlayStoreClientId =
      '545165014521-pcrgsc0v2ft0ame5hs7c5uv0ko654hkf.apps.googleusercontent.com';
  static const String webClientId =
      '545165014521-j89kvjdhljgjkpi491km7qdu7rkgj4or.apps.googleusercontent.com';
  static const String iosClientId =
      '545165014521-6lga42ejvda9e36l607gkllobevvpjfn.apps.googleusercontent.com';

  /// Get the appropriate client ID based on the current platform and build mode
  /// NOTE: This is for reference only. GoogleSignIn() without clientId parameter
  /// automatically uses the correct client ID from google-services.json
  static String get currentPlatformClientId {
    if (kIsWeb) {
      return webClientId;
    }

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        // Use debug client ID for debug builds, release for release builds
        return kDebugMode ? androidDebugClientId : androidReleaseClientId;
      case TargetPlatform.iOS:
        return iosClientId;
      default:
        return androidDebugClientId; // fallback
    }
  }

  /// Get Android client ID based on build mode
  static String get androidClientId {
    return kDebugMode ? androidDebugClientId : androidReleaseClientId;
  }

  /// Get specific client ID for Play Store (for manual testing)
  static String get playStoreClientId {
    return androidPlayStoreClientId;
  }

  /// Get all Android client IDs (for reference)
  static List<String> get allAndroidClientIds {
    return [
      androidDebugClientId,
      androidReleaseClientId,
      androidPlayStoreClientId,
    ];
  }

  /// Get all SHA-1 fingerprints for debugging
  static List<String> get allSHA1Fingerprints {
    return [
      debugSHA1,
      releaseSHA1,
      playStoreSHA1,
    ];
  }
}
