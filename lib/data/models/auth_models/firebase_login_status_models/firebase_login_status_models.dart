import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'firebase_login_status_models.g.dart';

/// Model for Firebase login status response
/// Follows the same pattern as other API response models in the app
@JsonSerializable()
class FirebaseLoginStatusModels extends Equatable {
  /// Status of the response (true/false)
  final bool? status;
  
  /// Response message
  final String? message;
  
  /// Data field containing the login status (1 for logged in, 0 for not logged in)
  final int? data;

  const FirebaseLoginStatusModels({
    this.status,
    this.message,
    this.data,
  });

  /// Creates an instance from JSON map
  factory FirebaseLoginStatusModels.fromJson(Map<String, dynamic> json) {
    return _$FirebaseLoginStatusModelsFromJson(json);
  }

  /// Converts instance to JSON map
  Map<String, dynamic> toJson() => _$FirebaseLoginStatusModelsToJson(this);

  /// Creates a copy with updated fields
  FirebaseLoginStatusModels copyWith({
    bool? status,
    String? message,
    int? data,
  }) {
    return FirebaseLoginStatusModels(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  /// Helper method to check if parent is logged in
  bool get isParentLoggedIn => data == 1;

  /// Helper method to check if parent is not logged in
  bool get isParentNotLoggedIn => data == 0;

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];

  @override
  String toString() {
    return 'FirebaseLoginStatusModels(status: $status, message: $message, data: $data)';
  }
}
