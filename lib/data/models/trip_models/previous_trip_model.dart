import 'package:bus/helper/custom_date_time.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

class BusInfo extends Equatable {
  final int? id;
  final String? name;

  const BusInfo({this.id, this.name});

  factory BusInfo.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return BusInfo(id: parseInt(json['id']), name: json['name']);
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name};
  }

  @override
  List<Object?> get props => [id, name];
}

class Attendant extends Equatable {
  final int? id;
  final String? type;
  final String? name;
  final String? logoPath;
  final AttendantPivot? pivot;

  const Attendant({this.id, this.type, this.name, this.logoPath, this.pivot});

  factory Attendant.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return Attendant(
      id: parseInt(json['id']),
      type: json['type'],
      name: json['name'],
      logoPath: json['logo_path'],
      pivot:
          json['pivot'] != null ? AttendantPivot.fromJson(json['pivot']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'name': name,
      'logo_path': logoPath,
      'pivot': pivot?.toJson(),
    };
  }

  @override
  List<Object?> get props => [id, type, name, logoPath, pivot];
}

class AttendantPivot extends Equatable {
  final int? tripId;
  final int? attendantId;

  const AttendantPivot({this.tripId, this.attendantId});

  factory AttendantPivot.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return AttendantPivot(
      tripId: parseInt(json['trip_id']),
      attendantId: parseInt(json['attendant_id']),
    );
  }

  Map<String, dynamic> toJson() {
    return {'trip_id': tripId, 'attendant_id': attendantId};
  }

  @override
  List<Object?> get props => [tripId, attendantId];
}

class PreviousTripModel extends Equatable {
  final int? id;
  final int? schoolId;
  final int? busId;
  final String? tripsDate;
  final String? tripType;
  final int? status;
  final String? latitude;
  final String? longitude;
  final String? attendanceType;
  final String? endAt;
  final String? createdAt;
  final String? updatedAt;
  final BusInfo? bus;
  final List<Attendant>? attendants;
  final List<RoutePoint>? routes;

  // UI display fields (may need to be calculated from API data)
  final String? busName;
  final String? startTime;
  final String? endTime;
  final String? supervisorName;
  final String? date;
  final int? supervisorId;

  const PreviousTripModel({
    this.id,
    this.schoolId,
    this.busId,
    this.tripsDate,
    this.tripType,
    this.status,
    this.latitude,
    this.longitude,
    this.attendanceType,
    this.endAt,
    this.createdAt,
    this.updatedAt,
    this.bus,
    this.attendants,
    this.routes,
    this.busName,
    this.startTime,
    this.endTime,
    this.supervisorName,
    this.date,
    this.supervisorId,
  });

  factory PreviousTripModel.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    // Parse routes if available
    List<RoutePoint>? routesList;
    if (json['routes'] != null) {
      routesList =
          (json['routes'] as List)
              .map((routeJson) => RoutePoint.fromJson(routeJson))
              .toList();
    }

    // Parse bus info if available
    BusInfo? busInfo;
    if (json['bus'] != null) {
      busInfo = BusInfo.fromJson(json['bus']);
    }

    // Parse attendants if available
    List<Attendant>? attendantsList;
    if (json['attendants'] != null) {
      attendantsList =
          (json['attendants'] as List)
              .map((attendantJson) => Attendant.fromJson(attendantJson))
              .toList();
    }

    // Extract date from trips_date field
    String? dateValue;
    if (json['trips_date'] != null) {
      try {
        final tripsDate = DateTime.parse(json['trips_date']);
        dateValue =
            "${tripsDate.year}-${tripsDate.month.toString().padLeft(2, '0')}-${tripsDate.day.toString().padLeft(2, '0')}";
      } catch (e) {
        dateValue = json['trips_date'];
      }
    } else {
      dateValue = json['date'];
    }

    // If date is still null, use current date
    if (dateValue == null) {
      final now = DateTime.now();
      dateValue =
          "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";
    }

    // Extract times from created_at/end_at if start_time/end_time not available
    String? startTimeValue = json['start_time'];
    if (startTimeValue == null && json['created_at'] != null) {
      // Convert server time to local time
      startTimeValue = CustomDateTime.convertToLocalTime(json['created_at']);
    } else if (startTimeValue != null) {
      // If start_time is provided directly, also convert it to local time
      // This handles cases where start_time might be a full datetime string
      try {
        DateTime serverTime = DateTime.parse(startTimeValue);
        DateTime localTime = serverTime.toLocal();
        startTimeValue =
            "${localTime.hour.toString().padLeft(2, '0')}:${localTime.minute.toString().padLeft(2, '0')}";
      } catch (e) {
        // If it's already in HH:MM format, it will throw an error, which is fine
        debugPrint("Start time is already in HH:MM format or invalid: $e");
      }
    }

    // If startTime is still null, use a default value
    startTimeValue ??= "00:00";

    String? endTimeValue = json['end_time'];
    if (endTimeValue == null && json['end_at'] != null) {
      // Convert server time to local time
      endTimeValue = CustomDateTime.convertToLocalTime(json['end_at']);
    } else if (endTimeValue == null && json['updated_at'] != null) {
      // Convert server time to local time
      endTimeValue = CustomDateTime.convertToLocalTime(json['updated_at']);
    } else if (endTimeValue != null) {
      // If end_time is provided directly, also convert it to local time
      // This handles cases where end_time might be a full datetime string
      try {
        DateTime serverTime = DateTime.parse(endTimeValue);
        DateTime localTime = serverTime.toLocal();
        endTimeValue =
            "${localTime.hour.toString().padLeft(2, '0')}:${localTime.minute.toString().padLeft(2, '0')}";
      } catch (e) {
        // If it's already in HH:MM format, it will throw an error, which is fine
        debugPrint("End time is already in HH:MM format or invalid: $e");
      }
    }

    // If endTime is still null, use a default value
    endTimeValue ??= "00:00";

    // Format times with AM/PM for better readability
    startTimeValue = CustomDateTime.formatTimeWithAmPm(startTimeValue);
    endTimeValue = CustomDateTime.formatTimeWithAmPm(endTimeValue);

    // Debug output to verify conversion
    debugPrint("Original created_at: ${json['created_at']}");
    debugPrint("Converted startTime: $startTimeValue");
    debugPrint("Original end_at: ${json['end_at']}");
    debugPrint("Converted endTime: $endTimeValue");

    // Ensure both times are in the same format (AM/PM)
    if (startTimeValue.contains("AM") || startTimeValue.contains("PM")) {
      if (!endTimeValue.contains("AM") && !endTimeValue.contains("PM")) {
        endTimeValue = CustomDateTime.formatTimeWithAmPm(endTimeValue);
      }
    }

    if (endTimeValue.contains("AM") || endTimeValue.contains("PM")) {
      if (!startTimeValue.contains("AM") && !startTimeValue.contains("PM")) {
        startTimeValue = CustomDateTime.formatTimeWithAmPm(startTimeValue);
      }
    }

    // Find supervisor name from attendants
    String? supervisorName;
    int? supervisorId;
    if (attendantsList != null && attendantsList.isNotEmpty) {
      // Try to find an admin attendant
      Attendant? adminAttendant;
      try {
        adminAttendant = attendantsList.firstWhere(
          (attendant) => attendant.type == 'admins',
        );
      } catch (_) {
        // If no admin found, use the first attendant
        adminAttendant = attendantsList.first;
      }

      supervisorName = adminAttendant.name;
      supervisorId = adminAttendant.id;
    }

    return PreviousTripModel(
      id: parseInt(json['id']),
      schoolId: parseInt(json['school_id']),
      busId: parseInt(json['bus_id']),
      tripsDate: json['trips_date'],
      tripType: json['trip_type'],
      status:
          json['status'] is bool
              ? (json['status'] as bool ? 1 : 0)
              : parseInt(json['status']),
      latitude: json['latitude'],
      longitude: json['longitude'],
      attendanceType: json['attendance_type'],
      endAt: json['end_at'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      bus: busInfo,
      attendants: attendantsList,
      routes: routesList,

      // UI display fields
      // Generate bus name from bus object or bus_id if not available
      busName:
          busInfo?.name ??
          json['bus_name'] ??
          "Bus ${json['bus_id'] ?? 'Unknown'}",
      startTime: startTimeValue,
      endTime: endTimeValue,
      supervisorName: supervisorName ?? json['supervisor_name'] ?? "Supervisor",
      date: dateValue,
      supervisorId: supervisorId ?? parseInt(json['supervisor_id']),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'school_id': schoolId,
      'bus_id': busId,
      'trips_date': tripsDate,
      'trip_type': tripType,
      'status': status,
      'latitude': latitude,
      'longitude': longitude,
      'attendance_type': attendanceType,
      'end_at': endAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };

    // Add optional fields only if they exist
    if (bus != null) {
      data['bus'] = bus!.toJson();
    }
    if (attendants != null) {
      data['attendants'] =
          attendants!.map((attendant) => attendant.toJson()).toList();
    }
    if (routes != null) {
      data['routes'] = routes!.map((route) => route.toJson()).toList();
    }
    if (busName != null) {
      data['bus_name'] = busName;
    }
    if (startTime != null) {
      data['start_time'] = startTime;
    }
    if (endTime != null) {
      data['end_time'] = endTime;
    }
    if (supervisorName != null) {
      data['supervisor_name'] = supervisorName;
    }
    if (date != null) {
      data['date'] = date;
    }
    if (supervisorId != null) {
      data['supervisor_id'] = supervisorId;
    }

    return data;
  }

  @override
  List<Object?> get props => [
    id,
    schoolId,
    busId,
    tripsDate,
    tripType,
    status,
    latitude,
    longitude,
    attendanceType,
    endAt,
    createdAt,
    updatedAt,
    bus,
    attendants,
    routes,
    busName,
    startTime,
    endTime,
    supervisorName,
    date,
    supervisorId,
  ];
}

class RoutePoint extends Equatable {
  final int? id;
  final int? tripId;
  final String? latitude;
  final String? longitude;
  final String? createdAt;
  final String? updatedAt;

  const RoutePoint({
    this.id,
    this.tripId,
    this.latitude,
    this.longitude,
    this.createdAt,
    this.updatedAt,
  });

  factory RoutePoint.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      if (value is String) {
        return int.tryParse(value);
      }
      if (value is double) return value.toInt();
      return null;
    }

    return RoutePoint(
      id: parseInt(json['id']),
      tripId: parseInt(json['trip_id']),
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  List<Object?> get props => [
    id,
    tripId,
    latitude,
    longitude,
    createdAt,
    updatedAt,
  ];
}

// ignore: must_be_immutable
class PreviousTripsResponse extends Equatable {
  final bool? status;
  final String? message;
  final bool? errors;
  List<PreviousTripModel>? data; // Removed 'final' to allow modification

  PreviousTripsResponse({this.status, this.message, this.errors, this.data});

  factory PreviousTripsResponse.fromJson(Map<String, dynamic> json) {
    // Check if the response has the new format with 'previous_trips'
    if (json.containsKey('previous_trips')) {
      return PreviousTripsResponse(
        status: true,
        message: json['message'] as String? ?? "Success",
        errors: json['errors'] as bool? ?? false,
        data:
            (json['previous_trips'] as List<dynamic>?)
                ?.map(
                  (e) => PreviousTripModel.fromJson(e as Map<String, dynamic>),
                )
                .toList(),
      );
    }

    // Fallback to the old format with 'data'
    return PreviousTripsResponse(
      status: json['status'] is bool ? json['status'] as bool? : null,
      message: json['message'] as String?,
      errors: json['errors'] as bool? ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map(
                (e) => PreviousTripModel.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
      'errors': errors,
      'data': data?.map((trip) => trip.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [status, message, errors, data];
}
