import 'package:bus/config/config_base.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/data/models/auth_models/firebase_login_status_models/firebase_login_status_models.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

/// Repository class for Firebase login status API
/// Follows the same pattern as other repository classes in the app
class FirebaseLoginStatusRepo {
  final _dio = NetworkService();

  /// Checks Firebase login status by sending Firebase ID token to backend
  /// 
  /// Parameters:
  /// - [idToken]: Firebase ID token from client authentication
  /// - [accessToken]: Firebase access token (optional)
  /// 
  /// Returns:
  /// - [DataState<FirebaseLoginStatusModels>]: Success with login status or failure with error
  Future<DataState<FirebaseLoginStatusModels>> checkFirebaseLoginStatus({
    String? idToken,
    String? accessToken,
  }) async {
    try {
      debugPrint("Checking Firebase login status with token");

      // Prepare request body following the API specification
      final Map<String, dynamic> requestBody = {
        "idToken": idToken,
        "firebase_token": fCMToken, // FCM token for notifications
      };

      // Add accessToken if provided
      if (accessToken != null) {
        requestBody["accessToken"] = accessToken;
      }

      final request = await _dio.post(
        url: ConfigBase.firebaseLoginStatus,
        body: requestBody,
      );

      debugPrint(
          "Firebase login status response - Status: ${request.statusCode}, Message: ${request.statusMessage}");
      debugPrint("Firebase login status response data: ${request.data}");

      // Check if request was successful
      if (request.statusCode == 200) {
        var loginStatusData = FirebaseLoginStatusModels.fromJson(request.data);
        debugPrint("Firebase login status check successful: ${loginStatusData.toString()}");
        return DataSuccess(loginStatusData);
      } else {
        debugPrint(
            "Firebase login status check failed with message: ${request.data.toString()}");
        return DataFailed(
          message: request.data['message'] ?? 'Unknown error occurred',
        );
      }
    } catch (e) {
      debugPrint("Exception in Firebase login status check: $e");
      Logger().e("Firebase login status error: $e");
      
      // Return appropriate error response
      return DataFailed(
        message: "Network error occurred while checking login status",
      );
    }
  }

  /// Alternative method for checking login status without tokens
  /// Useful for checking general Firebase login availability
  Future<DataState<FirebaseLoginStatusModels>> checkFirebaseLoginAvailability() async {
    try {
      debugPrint("Checking Firebase login availability");

      final request = await _dio.post(
        url: ConfigBase.firebaseLoginStatus,
        body: {
          "firebase_token": fCMToken,
        },
      );

      debugPrint(
          "Firebase login availability response - Status: ${request.statusCode}");
      debugPrint("Firebase login availability response data: ${request.data}");

      if (request.statusCode == 200) {
        var loginStatusData = FirebaseLoginStatusModels.fromJson(request.data);
        debugPrint("Firebase login availability check successful: ${loginStatusData.toString()}");
        return DataSuccess(loginStatusData);
      } else {
        debugPrint(
            "Firebase login availability check failed: ${request.data.toString()}");
        return DataFailed(
          message: request.data['message'] ?? 'Firebase login not available',
        );
      }
    } catch (e) {
      debugPrint("Exception in Firebase login availability check: $e");
      Logger().e("Firebase login availability error: $e");
      
      return DataFailed(
        message: "Network error occurred while checking Firebase login availability",
      );
    }
  }
}
