import 'dart:math';

import 'package:bus/config/config_base.dart';
import 'package:bus/data/models/route_models/route_point_model.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/network_serviecs.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class RouteRepo {
  final String token = CacheHelper.getString('token') ?? '';
  final _dio = NetworkService();

  Future<TripRouteResponse> getTripRoute({
    required int tripId,
  }) async {
    try {
      // Make API call to get trip route
      final response = await _dio.get(
        url: ConfigBase.tripRouteDetails + tripId.toString(),
        isAuth: true,
      );

      debugPrint("Trip Route API Response: ${response.data}");

      if (response.statusCode == 200) {
        // Parse the response
        final Map<String, dynamic> responseData = response.data;

        if (responseData['status'] == true && responseData['data'] != null) {
          // Extract trip data
          final tripData = responseData['data'];

          // Extract route points
          List<RoutePointModel> routePoints = [];
          if (tripData['routes'] != null && tripData['routes'] is List) {
            routePoints = (tripData['routes'] as List)
                .map((point) => RoutePointModel.fromJson(point))
                .toList();
          }

          // Create TripRouteModel
          final tripRouteModel = TripRouteModel(
            trip_id: tripData['id'],
            start_time: tripData['created_at'],
            end_time: tripData['end_at'],
            // Calculate estimated values based on route points
            total_distance: _calculateTotalDistance(routePoints),
            estimated_time: _calculateEstimatedTime(routePoints),
            route_points: routePoints,
          );

          return TripRouteResponse(
            status: true,
            message:
                responseData['message'] ?? "Route data fetched successfully",
            data: tripRouteModel,
          );
        } else {
          return TripRouteResponse(
            status: false,
            message: responseData['message'] ?? "No route data available",
            data: null,
          );
        }
      } else {
        return TripRouteResponse(
          status: false,
          message:
              "Failed to fetch route data. Status code: ${response.statusCode}",
          data: null,
        );
      }
    } catch (e) {
      debugPrint("Error fetching trip route: $e");
      // Return empty response on error
      return TripRouteResponse(
        status: false,
        message: "Failed to fetch route data: $e",
        data: null,
      );
    }
  }

  // Calculate total distance based on route points (in kilometers)
  double _calculateTotalDistance(List<RoutePointModel> routePoints) {
    if (routePoints.isEmpty || routePoints.length < 2) {
      return 0.0;
    }

    double totalDistance = 0.0;
    for (int i = 0; i < routePoints.length - 1; i++) {
      final point1 = routePoints[i];
      final point2 = routePoints[i + 1];

      if (point1.latitude != null &&
          point1.longitude != null &&
          point2.latitude != null &&
          point2.longitude != null) {
        // Convert string coordinates to double
        final lat1 = double.tryParse(point1.latitude!) ?? 0.0;
        final lng1 = double.tryParse(point1.longitude!) ?? 0.0;
        final lat2 = double.tryParse(point2.latitude!) ?? 0.0;
        final lng2 = double.tryParse(point2.longitude!) ?? 0.0;

        // Calculate distance between two points using Haversine formula
        totalDistance += _calculateDistance(lat1, lng1, lat2, lng2);
      }
    }

    return totalDistance;
  }

  // Calculate distance between two points using Haversine formula (in kilometers)
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Radius of the earth in km
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    final double distance = earthRadius * c;

    return distance;
  }

  // Convert degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // Calculate estimated time based on route points (in minutes)
  int _calculateEstimatedTime(List<RoutePointModel> routePoints) {
    if (routePoints.isEmpty || routePoints.length < 2) {
      return 0;
    }

    // Assuming average speed of 30 km/h in urban areas
    final double averageSpeedKmPerHour = 30.0;
    final double totalDistanceKm = _calculateTotalDistance(routePoints);

    // Calculate time in hours, then convert to minutes
    final double timeInHours = totalDistanceKm / averageSpeedKmPerHour;
    final int timeInMinutes = (timeInHours * 60).round();

    return timeInMinutes;
  }

  // Helper method to convert route points to LatLng for Google Maps
  List<LatLng> getRouteLatLng(List<RoutePointModel>? routePoints) {
    if (routePoints == null || routePoints.isEmpty) {
      return [];
    }

    return routePoints
        .where((point) => point.latitude != null && point.longitude != null)
        .map((point) {
      // Convert string coordinates to double
      final double lat = double.tryParse(point.latitude!) ?? 0.0;
      final double lng = double.tryParse(point.longitude!) ?? 0.0;
      return LatLng(lat, lng);
    }).toList();
  }
}
