import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_cubit.dart';
import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_states.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Example showing how to control Firebase login button visibility from dashboard
/// This demonstrates how administrators can enable/disable Google Sign-In
class DashboardFirebaseControlExample extends StatelessWidget {
  const DashboardFirebaseControlExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard - Firebase Login Control'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: BlocProvider(
        create: (context) => FirebaseLoginStatusCubit(),
        child: const _DashboardContent(),
      ),
    );
  }
}

class _DashboardContent extends StatelessWidget {
  const _DashboardContent();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title
          const Text(
            'Firebase Login Control Panel',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 20),
          
          // Description
          const Text(
            'Use this panel to control whether Google Sign-In button appears on the login screen.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 40),
          
          // Control buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // This would typically call an API to enable Firebase login
                    // For demo purposes, we'll simulate the response
                    _simulateEnableFirebaseLogin(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  icon: const Icon(Icons.check_circle),
                  label: const Text('Enable Google Login'),
                ),
              ),
              
              const SizedBox(width: 16),
              
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // This would typically call an API to disable Firebase login
                    // For demo purposes, we'll simulate the response
                    _simulateDisableFirebaseLogin(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  icon: const Icon(Icons.cancel),
                  label: const Text('Disable Google Login'),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Check status button
          ElevatedButton.icon(
            onPressed: () {
              context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            icon: const Icon(Icons.refresh),
            label: const Text('Check Current Status'),
          ),
          
          const SizedBox(height: 40),
          
          // Status display
          BlocBuilder<FirebaseLoginStatusCubit, FirebaseLoginStatusState>(
            builder: (context, state) {
              return Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: Colors.blue,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Current Status:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      if (state.isLoading) ...[
                        const Row(
                          children: [
                            CircularProgressIndicator(strokeWidth: 2),
                            SizedBox(width: 12),
                            Text('Checking status...'),
                          ],
                        ),
                      ] else if (state.isFailure) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.error, color: Colors.red),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Error',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red,
                                      ),
                                    ),
                                    Text(state.message ?? 'Unknown error'),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ] else if (state.hasLoginStatusData) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: state.isParentNotLoggedIn ? Colors.green[50] : Colors.orange[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: state.isParentNotLoggedIn ? Colors.green[200]! : Colors.orange[200]!,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    state.isParentNotLoggedIn ? Icons.check_circle : Icons.block,
                                    color: state.isParentNotLoggedIn ? Colors.green : Colors.orange,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    state.isParentNotLoggedIn 
                                      ? 'Google Login Enabled' 
                                      : 'Google Login Disabled',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: state.isParentNotLoggedIn ? Colors.green : Colors.orange,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text('Status Code: ${state.firebaseLoginStatusModels?.data}'),
                              Text('Message: ${state.firebaseLoginStatusModels?.message}'),
                              const SizedBox(height: 8),
                              Text(
                                state.isParentNotLoggedIn 
                                  ? 'Google Sign-In button will be visible on login screen'
                                  : 'Google Sign-In button will be hidden on login screen',
                                style: const TextStyle(fontStyle: FontStyle.italic),
                              ),
                            ],
                          ),
                        ),
                      ] else ...[
                        const Text('No status data available'),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
          
          const Spacer(),
          
          // API Information
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.api, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'API Information:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'Endpoint: POST /api/school/auth/firebase-login-status\n'
                  'Response: {"data": 1, "message": "Parent is logged in", "status": true}\n'
                  'data: 1 = Show button, data: 0 = Hide button',
                  style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Simulate enabling Firebase login (in real app, this would call backend API)
  void _simulateEnableFirebaseLogin(BuildContext context) {
    // This would typically make an API call to enable Firebase login
    // For demo, we'll just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Firebase login enabled successfully!'),
        backgroundColor: Colors.green,
      ),
    );
    
    // Refresh status
    context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();
  }

  // Simulate disabling Firebase login (in real app, this would call backend API)
  void _simulateDisableFirebaseLogin(BuildContext context) {
    // This would typically make an API call to disable Firebase login
    // For demo, we'll just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Firebase login disabled successfully!'),
        backgroundColor: Colors.orange,
      ),
    );
    
    // Refresh status
    context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();
  }
}
