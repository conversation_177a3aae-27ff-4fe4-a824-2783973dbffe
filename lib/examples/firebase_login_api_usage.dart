import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// مثال بسيط على كيفية استخدام Firebase Login Status API
/// هذا المثال يوضح كيفية التحقق من حالة تسجيل الدخول بـ Firebase
class FirebaseLoginApiUsageExample extends StatelessWidget {
  const FirebaseLoginApiUsageExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Login API Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            const Text(
              'استخدام Firebase Login Status API',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // زر للتحقق من حالة Firebase login
            ElevatedButton(
              onPressed: () {
                context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();
              },
              child: const Text('تحقق من حالة Firebase Login'),
            ),
            
            const SizedBox(height: 20),
            
            // عرض النتيجة
            BlocBuilder<FirebaseLoginStatusCubit, dynamic>(
              builder: (context, state) {
                if (state.isLoading) {
                  return const CircularProgressIndicator();
                }
                
                if (state.isFailure) {
                  return Text(
                    'خطأ: ${state.message}',
                    style: const TextStyle(color: Colors.red),
                  );
                }
                
                if (state.hasLoginStatusData) {
                  return Column(
                    children: [
                      Text(
                        'حالة Firebase Login: ${state.isParentNotLoggedIn ? "مفعل" : "معطل"}',
                        style: TextStyle(
                          color: state.isParentNotLoggedIn ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text('الرسالة: ${state.firebaseLoginStatusModels?.message}'),
                      Text('البيانات: ${state.firebaseLoginStatusModels?.data}'),
                    ],
                  );
                }
                
                return const Text('لا توجد بيانات');
              },
            ),
            
            const SizedBox(height: 40),
            
            // معلومات الـ API
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات الـ API:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('Endpoint: POST /api/school/auth/firebase-login-status'),
                  Text('Response: {"data": 1, "message": "Parent is logged in", "status": true}'),
                  Text('data: 1 = إظهار الزر, data: 0 = إخفاء الزر'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
