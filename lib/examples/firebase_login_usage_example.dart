import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_cubit.dart';
import 'package:bus/bloc/login_cubit/login_cubit.dart';
import 'package:bus/widgets/firebase_login_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Example showing how to use Firebase login status API and widget
/// This demonstrates the complete integration in a real screen
class FirebaseLoginUsageExample extends StatelessWidget {
  const FirebaseLoginUsageExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Login Example'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: MultiBlocProvider(
        providers: [
          // Provide Firebase login status cubit
          BlocProvider(
            create: (context) => FirebaseLoginStatusCubit(),
          ),
          // Provide login cubit (usually already provided at app level)
          BlocProvider(
            create: (context) => LoginCubit(),
          ),
        ],
        child: const _FirebaseLoginContent(),
      ),
    );
  }
}

class _FirebaseLoginContent extends StatelessWidget {
  const _FirebaseLoginContent();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title
          const Text(
            'Firebase Login Status Demo',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 20),
          
          // Description
          const Text(
            'This example demonstrates how to use the Firebase login status API to conditionally show/hide the Google Sign-In button.',
            style: TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 40),
          
          // Firebase Login Button Widget
          // This will automatically check login status and show/hide accordingly
          FirebaseLoginButtonWidget(
            onLoginSuccess: () {
              // Handle successful login
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Successfully signed in with Google!'),
                  backgroundColor: Colors.green,
                ),
              );
              
              // Navigate to dashboard or home screen
              // Navigator.pushReplacementNamed(context, '/dashboard');
            },
            onLoginError: (error) {
              // Handle login error
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Login failed: $error'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            buttonText: 'Continue with Google',
            buttonStyle: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          
          const SizedBox(height: 40),
          
          // Manual status check button for testing
          ElevatedButton(
            onPressed: () {
              context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[300],
              foregroundColor: Colors.black87,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('Refresh Login Status'),
          ),
          
          const SizedBox(height: 20),
          
          // Status display
          BlocBuilder<FirebaseLoginStatusCubit, dynamic>(
            builder: (context, state) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Current Status:',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('State: ${state.rStates}'),
                      if (state.message != null) 
                        Text('Message: ${state.message}'),
                      if (state.firebaseLoginStatusModels != null) ...[
                        Text('Login Status Data: ${state.firebaseLoginStatusModels?.data}'),
                        Text('Is Parent Logged In: ${state.isParentLoggedIn}'),
                        Text('Is Parent Not Logged In: ${state.isParentNotLoggedIn}'),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
          
          const Spacer(),
          
          // Additional info
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'How it works:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  '1. Widget checks Firebase login status via API\n'
                  '2. If status is "not logged in" (data: 0), shows Google button\n'
                  '3. If status is "logged in" (data: 1), hides Google button\n'
                  '4. Button handles complete Google Sign-In flow',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Alternative simple usage example for integration in existing screens
class SimpleFirebaseLoginExample extends StatelessWidget {
  const SimpleFirebaseLoginExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => FirebaseLoginStatusCubit(),
      child: Column(
        children: [
          // Your existing login form widgets here
          // ...
          
          const SizedBox(height: 20),
          
          // Add Firebase login button
          const FirebaseLoginButtonWidget(),
          
          // Your other widgets here
          // ...
        ],
      ),
    );
  }
}
