// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBevuGdVtGOlyDmrZJLJOk0jVu-3XbOaRM',
    appId: '1:545165014521:web:your-web-app-id7db928',
    messagingSenderId: '545165014521',
    projectId: 'test-5c820',
    authDomain: 'test-5c820.firebaseapp.com',
    storageBucket: 'test-5c820.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDFMXMT1qlX4juzd4X3RVVdxqXJtRSvsPo',
    appId: '1:545165014521:android:6d7ea6bab331153b7db928',
    messagingSenderId: '545165014521',
    projectId: 'test-5c820',
    storageBucket: 'test-5c820.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBevuGdVtGOlyDmrZJLJOk0jVu-3XbOaRM',
    appId: '1:545165014521:ios:25d4ec2dc3a419017db928',
    messagingSenderId: '545165014521',
    projectId: 'test-5c820',
    storageBucket: 'test-5c820.firebasestorage.app',
    iosBundleId: 'com.busaty.school',
  );
}
