import 'package:bus/translations/local_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../widgets/student_widgets/custom_container_dialog_w.dart';

class Helpers {
  static customShowDialog(
    BuildContext context, {
    Function()? onTapLocation,
    Function()? onTapShow,
    Function()? onTapEdit,
    Function()? onTapDelete,
    required Offset position,
    List<PopupMenuItem> items = const [],
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    Offset offset = overlay.localToGlobal(Offset.zero);

    final dx =
        context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
    final dy = position.dy + 15;

    showMenu(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(15.sp))),
      context: context,
      position: RelativeRect.fromLTRB(
        dx,
        dy,
        0,
        0,
      ),
      items: [
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: Icons.location_on,
              name: AppStrings.location.tr(),
              onTap: onTapLocation,
            ),
          ),
        ),
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: Icons.remove_red_eye,
              name: AppStrings.show.tr(),
              onTap: onTapShow,
            ),
          ),
        ),
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.create,
              name: AppStrings.edit.tr(),
              onTap: onTapEdit,
            ),
          ),
        ),
        ...items,
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.delete,
              name: AppStrings.delete.tr(),
              onTap: onTapDelete,
            ),
          ),
        ),
      ],
    );
  }

  static customClassroomsShowDialog(
    BuildContext context, {
    Function()? onTapEdit,
    Function()? onTapDelete,
    required Offset position,
    List<PopupMenuItem> items = const [],
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    Offset offset = overlay.localToGlobal(Offset.zero);

    final dx =
        context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
    final dy = position.dy + 15;

    showMenu(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(15.sp))),
      context: context,
      position: RelativeRect.fromLTRB(
        dx,
        dy,
        0,
        0,
      ),
      items: [
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.create,
              name: AppStrings.edit.tr(),
              onTap: onTapEdit,
            ),
          ),
        ),
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.delete,
              name: AppStrings.delete.tr(),
              onTap: onTapDelete,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  static customAbsenceShowDialog(
    BuildContext context, {
    Function()? onTapShow,
    required Offset position,
    List<PopupMenuItem> items = const [],
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    Offset offset = overlay.localToGlobal(Offset.zero);

    final dx =
        context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
    final dy = position.dy + 15;

    showMenu(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(15.sp))),
      context: context,
      position: RelativeRect.fromLTRB(
        dx,
        dy,
        0,
        0,
      ),
      items: [
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: Icons.remove_red_eye,
              name: AppStrings.show.tr(),
              onTap: onTapShow,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  static customBusShowDialog(
    BuildContext context, {
    Function()? onTapShow,
    Function()? onTapEdit,
    Function()? onTapDelete,
    Function()? onTapAddToBus,
    required Offset position,
    List<PopupMenuItem> items = const [],
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    Offset offset = overlay.localToGlobal(Offset.zero);

    final dx =
        context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
    final dy = position.dy + 15;

    showMenu(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(15.sp))),
      context: context,
      position: RelativeRect.fromLTRB(
        dx,
        dy,
        0,
        0,
      ),
      items: [
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: Icons.remove_red_eye,
              name: AppStrings.show.tr(),
              onTap: onTapShow,
            ),
          ),
        ),
        ...items,
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.create,
              name: AppStrings.edit.tr(),
              onTap: onTapEdit,
            ),
          ),
        ),
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.delete,
              name: AppStrings.delete.tr(),
              onTap: onTapDelete,
            ),
          ),
        ),
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.add,
              name: AppStrings.addStudentToBus.tr(),
              onTap: onTapAddToBus,
            ),
          ),
        ),
      ],
    );
  }
}
