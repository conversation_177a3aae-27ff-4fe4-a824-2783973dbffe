import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SBox extends StatelessWidget {
  final double h;
  final double w;
  final bool fullWidth;
  final Widget? child;
  const SBox({
    super.key,
    this.h = 8.0,
    this.w = 0.0,
    this.fullWidth = false,
    this.child,
  });

  @override
  SizedBox build(BuildContext context) {
    return SizedBox(width: fullWidth ? 1.sw : w.w, height: h.w, child: child);
  }
}

final fullWidth = 1.sw;
