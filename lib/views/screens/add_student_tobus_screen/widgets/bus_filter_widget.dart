import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:logger/logger.dart';

import 'package:bus/bloc/buses_cubit/buses_cubit.dart';
import 'package:bus/bloc/buses_cubit/buses_states.dart';
import 'package:bus/data/models/buses_models/buses_info_models.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';

class BusFilterWidget extends StatelessWidget {
  final String currentBusId;
  final int? selectedBusId;
  final Function(int?) onBusSelected;
  final Logger logger;

  const BusFilterWidget({
    Key? key,
    required this.currentBusId,
    required this.selectedBusId,
    required this.onBusSelected,
    required this.logger,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusesCubit, BusesState>(
      builder: (context, state) {
        if (state is BusesLoadingStates) {
          return _buildLoadingState();
        } else if (state is BusesSuccessStates) {
          return _buildSuccessState(state);
        } else if (state is BusesErrorStates) {
          return _buildErrorState(context);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              color: TColor.mainColor,
              strokeWidth: 2,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Loading buses...',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessState(BusesSuccessStates state) {
    // Filter out the current bus from the dropdown list
    final currentBusIdInt = int.parse(currentBusId);
    List<BusesInfoModel> allBuses = state.busesDataModels?.data ?? [];

    // Remove the current bus from the list
    List<BusesInfoModel> filteredBuses =
        allBuses.where((bus) => bus.id != currentBusIdInt).toList();

    List<BusesInfoModel> buses = [
      BusesInfoModel(id: 0, name: AppStrings.selectBus.tr()),
      ...filteredBuses,
    ];

    logger.i(
      "Filtered buses: Showing ${filteredBuses.length} buses (excluded current bus ID: $currentBusIdInt)",
    );

    // If no other buses available, show a message
    if (filteredBuses.isEmpty) {
      return _buildNoBusesMessage();
    }

    return _buildBusDropdown(buses);
  }

  Widget _buildNoBusesMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.amber.shade700, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'لا توجد باصات أخرى متاحة للفلترة',
              style: TextStyle(
                color: Colors.amber.shade700,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusDropdown(List<BusesInfoModel> buses) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              selectedBusId != null && selectedBusId != 0
                  ? TColor.mainColor.withValues(alpha: 0.3)
                  : Colors.grey.shade200,
          width: selectedBusId != null && selectedBusId != 0 ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color:
                selectedBusId != null && selectedBusId != 0
                    ? TColor.mainColor.withValues(alpha: 0.1)
                    : Colors.black.withValues(alpha: 0.05),
            blurRadius: selectedBusId != null && selectedBusId != 0 ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildBusIcon(),
          const SizedBox(width: 12),
          Expanded(child: _buildDropdownContent(buses)),
          if (selectedBusId != null && selectedBusId != 0) _buildClearButton(),
        ],
      ),
    );
  }

  Widget _buildBusIcon() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color:
            selectedBusId != null && selectedBusId != 0
                ? TColor.mainColor.withValues(alpha: 0.15)
                : TColor.mainColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        selectedBusId != null && selectedBusId != 0
            ? Icons.directions_bus
            : Icons.directions_bus_outlined,
        color: TColor.mainColor,
        size: 18,
      ),
    );
  }

  Widget _buildDropdownContent(List<BusesInfoModel> buses) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (selectedBusId != null && selectedBusId != 0)
          const Text(
            'Filtered by:',
            style: TextStyle(
              fontSize: 11,
              color: TColor.mainColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        DropdownButtonHideUnderline(
          child: DropdownButton<int>(
            isExpanded: true,
            hint: Text(
              AppStrings.selectBus.tr(),
              style: TextStyle(color: Colors.grey.shade600),
            ),
            value: selectedBusId ?? 0,
            items: buses.map((value) => _buildDropdownItem(value)).toList(),
            onChanged: _handleBusSelection,
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: TColor.mainColor,
            ),
          ),
        ),
      ],
    );
  }

  DropdownMenuItem<int> _buildDropdownItem(BusesInfoModel bus) {
    return DropdownMenuItem<int>(
      value: bus.id,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            if (bus.id != 0) ...[
              Icon(
                Icons.directions_bus,
                size: 16,
                color:
                    bus.id == selectedBusId
                        ? TColor.mainColor
                        : Colors.grey.shade600,
              ),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                bus.name ?? '',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: bus.id == 0 ? FontWeight.w400 : FontWeight.w500,
                  color:
                      bus.id == 0
                          ? Colors.grey.shade600
                          : bus.id == selectedBusId
                          ? TColor.mainColor
                          : Colors.grey.shade800,
                ),
              ),
            ),
            if (bus.id == selectedBusId && bus.id != 0)
              const Icon(Icons.check_circle, size: 16, color: TColor.mainColor),
          ],
        ),
      ),
    );
  }

  Widget _buildClearButton() {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      child: IconButton(
        onPressed: () {
          onBusSelected(null);
          HapticFeedback.lightImpact();
        },
        icon: const Icon(Icons.clear, color: TColor.mainColor, size: 20),
        tooltip: 'Clear filter',
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
      ),
    );
  }

  void _handleBusSelection(int? value) async {
    if (value == selectedBusId) return;

    HapticFeedback.selectionClick();
    onBusSelected(value);
  }

  Widget _buildErrorState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade600, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Failed to load buses. Tap to retry.',
              style: TextStyle(color: Colors.red.shade700, fontSize: 14),
            ),
          ),
          TextButton(
            onPressed: () {
              context.read<BusesCubit>().getAllBuses();
            },
            child: Text(
              'Retry',
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
