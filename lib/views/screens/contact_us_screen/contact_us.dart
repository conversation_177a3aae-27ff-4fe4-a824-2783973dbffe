import 'package:bus/config/form_styles.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/user_models/user_models.dart';
import 'package:bus/data/repo/user_repo.dart';
import 'package:bus/helper/data_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';

class ContactUsScreen extends StatefulWidget {
  // static const routeName = '/contact-us';
  static const String routeName = PathRouteName.contactUs;
  const ContactUsScreen({super.key});

  @override
  ContactUsScreenState createState() => ContactUsScreenState();
}

class ContactUsScreenState extends State<ContactUsScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _problemController = TextEditingController();
  bool _isSending = false;
  UserModel? _userModel;

  Future<void> _loadUserProfile() async {
    try {
      final userRepo = UserRepo();
      final response = await userRepo.repo();
      if (response is DataSuccess<UserModel>) {
        setState(() {
          _userModel = response.data;
          _nameController.text = _userModel?.name ?? '';
          _emailController.text = _userModel?.email ?? '';
        });
      }
    } catch (e) {
      print('Error loading user profile: $e');
    }
  }

  @override
  void initState() {
    print('[ContactUsScreen] initState called');
    super.initState();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    _loadUserProfile();

    // Add listeners to controllers
    _nameController.addListener(() {
      print('[ContactUsScreen] Name changed: ${_nameController.text}');
    });

    _emailController.addListener(() {
      print('[ContactUsScreen] Email changed: ${_emailController.text}');
    });

    _problemController.addListener(() {
      print('[ContactUsScreen] Problem changed: ${_problemController.text}');
    });
  }

  @override
  void dispose() {
    print('[ContactUsScreen] dispose called');
    _nameController.dispose();
    _emailController.dispose();
    _problemController.dispose();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: TColor.primary.withOpacity(0.75),
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.contactUs.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
        rightWidget: const SizedBox(),
      ),
      body: AbsorbPointer(
        absorbing: _isSending,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SizedBox(height: 20.h),
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppStrings.getInTouch.tr(),
                            style: TextStyle(
                              fontSize: 20.sp,
                              fontWeight: FontWeight.w600,
                              color: TColor.text,
                            ),
                          ),
                          Text(
                            AppStrings.wedLoveToHear.tr(),
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: TColor.textForm,
                            ),
                          ),
                          SizedBox(height: 24.h),
                          TextFormField(
                            controller: _nameController,
                            readOnly: true,
                            decoration: FormStyles.textFieldStyle(
                              hintTextStr: AppStrings.enterYourName.tr(),
                              prefixIcon: const Icon(Icons.person,
                                  color: TColor.iconInputColor),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppStrings.pleaseEnterName.tr();
                              }
                              return null;
                            },
                          ),
                          SizedBox(height: 20.h),
                          TextFormField(
                            controller: _emailController,
                            readOnly: true,
                            decoration: FormStyles.textFieldStyle(
                              hintTextStr: AppStrings.enterYourEmail.tr(),
                              prefixIcon: const Icon(Icons.email,
                                  color: TColor.iconInputColor),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppStrings.pleaseEnterEmail.tr();
                              }
                              if (!value.contains('@')) {
                                return AppStrings.pleaseValidEmail.tr();
                              }
                              return null;
                            },
                          ),
                          SizedBox(height: 20.h),
                          TextFormField(
                            controller: _problemController,
                            maxLines: 4,
                            maxLength: 1000,
                            decoration: FormStyles.textFieldStyle(
                              hintTextStr: AppStrings.describeProblem.tr(),
                              prefixIcon: const Icon(Icons.message,
                                  color: TColor.iconInputColor),
                            ).copyWith(
                              counterStyle: TextStyle(
                                color: TColor.textForm,
                                fontSize: 12.sp,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppStrings.pleaseDescribeProblem.tr();
                              }
                              if (value.length > 1000) {
                                return AppStrings.messageTooLong.tr();
                              }
                              return null;
                            },
                          ),
                          SizedBox(height: 24.h),
                          Container(
                            padding: EdgeInsets.all(12.w),
                            decoration: BoxDecoration(
                              color: TColor.fillFormField,
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  AppStrings.contactDirectly.tr(),
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: TColor.textForm,
                                  ),
                                ),
                                SizedBox(height: 8.h),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        '<EMAIL>',
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          color: TColor.text,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                        Clipboard.setData(
                                          const ClipboardData(
                                            text: '<EMAIL>',
                                          ),
                                        ).then((_) {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                AppStrings.emailCopied.tr(),
                                              ),
                                              backgroundColor: Colors.green,
                                              duration:
                                                  const Duration(seconds: 1),
                                            ),
                                          );
                                        });
                                      },
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 12.w,
                                          vertical: 6.h,
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              TColor.primary.withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(6.r),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.copy,
                                              size: 16.sp,
                                              color: TColor.primary,
                                            ),
                                            SizedBox(width: 4.w),
                                            Text(
                                              AppStrings.copy.tr(),
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: TColor.primary,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 24.h),
                          SizedBox(
                            width: double.infinity,
                            height: 48.h,
                            child: ElevatedButton(
                              onPressed: _isSending
                                  ? null
                                  : () async {
                                      if (_formKey.currentState!.validate()) {
                                        setState(() {
                                          _isSending = true;
                                        });

                                        // Show loading indicator
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Row(
                                              children: [
                                                const SizedBox(
                                                  width: 20,
                                                  height: 20,
                                                  child:
                                                      CircularProgressIndicator(
                                                    strokeWidth: 2,
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                            Color>(
                                                      Colors.white,
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(width: 16),
                                                Text(AppStrings.sending.tr()),
                                              ],
                                            ),
                                            duration:
                                                const Duration(seconds: 1),
                                          ),
                                        );

                                        try {
                                          await _sendEmail();
                                          // Show success message
                                          if (mounted) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  AppStrings.emailSent.tr(),
                                                ),
                                                backgroundColor: Colors.green,
                                              ),
                                            );
                                            // Clear form fields
                                            _nameController.clear();
                                            _emailController.clear();
                                            _problemController.clear();
                                          }
                                        } catch (e) {
                                          // Show error message
                                          if (mounted) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                  AppStrings.failedToSend
                                                      .tr()
                                                      .replaceAll(
                                                        '{error}',
                                                        e.toString(),
                                                      ),
                                                ),
                                                backgroundColor: Colors.red,
                                              ),
                                            );
                                          }
                                        } finally {
                                          if (mounted) {
                                            setState(() {
                                              _isSending = false;
                                            });
                                          }
                                        }
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    _isSending ? TColor.grey5 : TColor.primary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.r),
                                ),
                              ),
                              child: Text(
                                AppStrings.submit.tr(),
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: TColor.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _sendEmail() async {
    print('[ContactUsScreen] Starting email send process');

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Text(AppStrings.sending.tr()),
          ],
        ),
        duration: const Duration(seconds: 1),
      ),
    );

    print('[ContactUsScreen] Preparing email with SMTP server');
    final smtpServer = SmtpServer(
      'busaty.org',
      port: 465,
      ssl: true,
      username: '<EMAIL>',
      password: 'Ht~Gnj(kI*a;',
    );

    final message = Message()
      ..from = Address('<EMAIL>', 'Busaty Contact')
      ..recipients.add('<EMAIL>')
      ..subject =
          'Contact Form - ${_nameController.text} (${_emailController.text})'
      ..text = 'Problem:\n${_problemController.text}';

    print('[ContactUsScreen] Attempting to send email');
    try {
      await send(message, smtpServer);
      print('[ContactUsScreen] Email sent successfully');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppStrings.emailSent.tr()),
            backgroundColor: Colors.green,
          ),
        );

        // Clear form after successful send
        setState(() {
          _nameController.clear();
          _emailController.clear();
          _problemController.clear();
          print('[ContactUsScreen] Form cleared after successful send');
        });
      }
    } catch (e) {
      print('[ContactUsScreen] Failed to send email: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              AppStrings.failedToSend.tr().replaceAll('{error}', e.toString()),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
