import 'package:bus/bloc/login_cubit/login_cubit.dart';
import 'package:bus/bloc/login_cubit/login_states.dart';
import 'package:bus/bloc/profile_cubit/profile_cubit.dart';
import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_cubit.dart';
import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:flutter/foundation.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/deep_link.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/helper/response_state.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/complete_profile_screen/complete_profile_screen.dart';
import 'package:bus/views/screens/forget_password_screen/forget_password_screen.dart';
import 'package:bus/views/screens/layout_screen/layout_screen.dart';
import 'package:bus/views/screens/send_code_screen/send_code_screen.dart';
import 'package:bus/widgets/custom_background_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gcaptcha_v3/recaptca_config.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_sign_in/google_sign_in.dart';

class LoginScreen extends StatefulWidget {
  static const String routeName = PathRouteName.login;
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool securityCheck = true;
  bool isChecked = true;
  String? email;
  String? password;
  final _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    initializeFCM();
    // Check Firebase login availability when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();
    });
    super.initState();
  }

  void _onLoginSuccess(LoginState state) {
    if (isChecked) {
      CacheHelper.putString("token", state.loginModels!.token!);
    }
    token = state.loginModels!.token;

    // Log the current value for debugging
    debugPrint("_onLoginSuccess - isGoogleSignIn: $isGoogleSignIn");

    // Navigate to home screen
    if (mounted) {
      Navigator.pushNamedAndRemoveUntil(
          context, LayoutScreen.routeName, (route) => false);
    }

    debugPrint("llltoken ");
    debugPrint(state.loginModels!.identity_preview.toString());
    debugPrint(token);

    if (state.loginModels!.identity_preview == true) {
      Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(
            builder: (context) => const LayoutScreen(),
          ),
          (_) => false);
    } else {
      Navigator.pushReplacementNamed(context, CompleteProfileScreen.routeName);
    }
  }

  void _onNeedVerification() {
    Navigator.pushReplacementNamed(context, SendCodeScreen.routeName);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        backgroundColor: TColor.redAccent,
        content: CustomText(
          text: "please verify your email",
          color: TColor.white,
        ),
      ),
    );
  }

  void execute() => RecaptchaHandler.executeV3();
  @override
  Widget build(BuildContext context) {
    debugPrint('**token: $token');
    return Scaffold(
      body: SingleChildScrollView(
        child: CustomBackgroundImage(
          child: Column(
            children: [
              const SBox(h: 10),
              Image.asset(
                assetsImages("logo.png"),
                height: 121.w,
                width: 124.w,
              ),
              CustomText(
                text: tr('Busaty - School'),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const SBox(h: 40),
              CustomText(
                text: AppStrings.login.tr(),
                color: TColor.white,
                fontW: FontWeight.w700,
                fontSize: 20,
              ),
              const SBox(h: 30),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.white,
                  boxShadow: [
                    BoxShadow(
                      color:
                          Colors.black.withAlpha(51), // 0.2 opacity = 51 alpha
                      blurRadius: 3,
                      offset: const Offset(0, 4), // changes position of shadow
                    ),
                  ],
                ),
                width: 354.w,
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        const SBox(h: 40),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.mail_outline_outlined,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          hintText: AppStrings.email.tr(),
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          checkValidatorFunc: true,
                          validatorFunc: (value) {
                            if (value == null || value.isEmpty) {
                              return AppStrings.email.tr();
                            } else if (value.length < 6) {
                              return "this field should be more than 6 characters long";
                            } else if (value.contains('+')) {
                              return "لا يمكن استخدام علامة + في البريد الإلكتروني";
                            }
                            return null;
                          },
                          radiusNumber: 15.0,
                          requierdNumber: 6,
                          onChanged: (value) {
                            email = value;
                          },
                        ),
                        const SBox(h: 15),
                        CustomFormFieldWithBorder(
                          prefix: const Icon(
                            Icons.lock_outline,
                            color: TColor.iconInputColor,
                          ),
                          formFieldWidth: 307,
                          borderColor: TColor.fillFormFieldB,
                          fillColor: TColor.fillFormFieldB,
                          radiusNumber: 15.0,
                          hintText: AppStrings.password.tr(),
                          security: securityCheck,
                          requierdNumber: 6,
                          onChanged: (value) {
                            password = value;
                          },
                          validation: AppStrings.password.tr(),
                          suffix: InkWell(
                            onTap: () {
                              setState(() {
                                securityCheck = !securityCheck;
                              });
                            },
                            child: securityCheck
                                ? const Icon(
                                    Icons.visibility_off,
                                    color: TColor.iconInputColor,
                                  )
                                : const Icon(
                                    Icons.visibility_outlined,
                                    color: TColor.iconInputColor,
                                  ),
                          ),
                        ),
                        const SBox(h: 15),
                        SizedBox(
                          width: 307.w,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              InkWell(
                                onTap: () {
                                  Navigator.pushNamed(
                                      context, ForgetPasswordScreen.routeName);
                                },
                                child: CustomText(
                                  text: AppStrings.forgetPassword.tr(),
                                  color: TColor.textLogin,
                                  fontW: FontWeight.w500,
                                  fontSize: 14,
                                ),
                              ),
                              Row(
                                children: [
                                  CustomText(
                                    text: AppStrings.remember.tr(),
                                    color: TColor.textLogin,
                                    fontW: FontWeight.w500,
                                    fontSize: 14,
                                  ),
                                  SizedBox(
                                    child: Checkbox(
                                      activeColor: TColor.mainColor,
                                      value: isChecked,
                                      onChanged: (value) {
                                        setState(() {
                                          isChecked = value!;
                                        });
                                      },
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                        const SBox(h: 50),
                        BlocConsumer<LoginCubit, LoginState>(
                          listener: (context, state) {
                            if (state.rStates == ResponseState.success) {
                              CacheHelper.putInt(
                                  "profileID", state.loginModels!.id!);
                              // Set and save the Google sign-in flag to false for regular login
                              isGoogleSignIn = false;
                              CacheHelper.putBool("isGoogleSignIn", false);
                              debugPrint(
                                  "Regular Login - Set isGoogleSignIn to false");

                              // Verify Google sign-in flag is set correctly for regular login
                              final isGoogle =
                                  CacheHelper.getBool("isGoogleSignIn") ??
                                      false;
                              debugPrint(
                                  "Regular Login success - isGoogleSignIn from cache: $isGoogle");

                              _onLoginSuccess(state);

                              token = state.loginModels!.token;
                              // Navigator.pop(context);
                              context.read<ProfileCubit>().getProfile();
                            } else if (state.rStates == ResponseState.failure) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text:
                                        "please enter correct email or password",
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            } else if (state.rStates ==
                                ResponseState.needVerivecation) {
                              debugPrint(tempToken);
                              // CacheHelper.putString("token", token!);
                              // token = states.loginModels!.token;
                              Navigator.popAndPushNamed(
                                  context, SendCodeScreen.routeName);
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  backgroundColor: TColor.redAccent,
                                  content: CustomText(
                                    text: "please verify your email",
                                    color: TColor.white,
                                  ),
                                ),
                              );
                            }
                          },
                          builder: (context, state) {
                            if (state.rStates != ResponseState.loading) {
                              return CustomButton(
                                text: AppStrings.login.tr(),
                                onTap: () {
                                  if (_formKey.currentState!.validate()) {
                                    _formKey.currentState!.save();
                                    context.read<LoginCubit>().login(
                                          email: email,
                                          password: password,
                                        );
                                  }
                                },
                                width: 307,
                                height: 48,
                                radius: 15,
                                borderColor: TColor.mainColor,
                                bgColor: TColor.mainColor,
                              );
                            } else {
                              return const CircularProgressIndicator(
                                color: TColor.mainColor,
                              );
                            }
                          },
                        ),
                        const SBox(h: 20),
                        _buildGoogleButton(),
                        const SBox(h: 40),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomText(
                              text: AppStrings.notHaveAccount.tr(),
                              fontSize: 16,
                              fontW: FontWeight.w500,
                              color: TColor.textLogin,
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pushNamed(
                                    context, PathRouteName.signup);
                              },
                              child: CustomText(
                                text: AppStrings.createAccount.tr(),
                                fontSize: 16,
                                fontW: FontWeight.w500,
                                color: TColor.mainColor,
                              ),
                            ),
                          ],
                        ),
                        15.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoogleButton() {
    return BlocBuilder<FirebaseLoginStatusCubit, FirebaseLoginStatusState>(
      builder: (context, firebaseStatusState) {
        // Show loading indicator while checking Firebase login status
        if (firebaseStatusState.isLoading) {
          return Container(
            width: 307.w,
            height: 48.h,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Colors.grey.shade300,
                width: 1,
              ),
            ),
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        }

        // Hide Google button if Firebase login is disabled from dashboard
        // or if there's an error checking the status
        if (firebaseStatusState.isFailure ||
            (firebaseStatusState.hasLoginStatusData &&
                firebaseStatusState.isParentLoggedIn)) {
          return const SizedBox.shrink(); // Hide the button
        }

        // Show Google button if Firebase login is enabled from dashboard
        if (firebaseStatusState.hasLoginStatusData &&
            firebaseStatusState.isParentNotLoggedIn) {
          return Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => handleGoogleLogin(context),
              borderRadius: BorderRadius.circular(15),
              child: Container(
                width: 307.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color:
                          Colors.black.withAlpha(26), // 0.1 opacity = 26 alpha
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      "assets/images/google_icon.png",
                      width: 24.w,
                      height: 24.h,
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      'login_with_google'.tr(),
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        // Default case - hide button if status is unknown
        return const SizedBox.shrink();
      },
    );
  }

  Future<void> handleGoogleLogin(BuildContext context) async {
    // Store a reference to the LoginCubit before any async operations
    final LoginCubit loginCubit = context.read<LoginCubit>();

    try {
      debugPrint('=== Google Sign In Debug ===');
      debugPrint('Package Name: com.busaty.school');
      debugPrint('Debug Mode: $kDebugMode');
      debugPrint('Platform: $defaultTargetPlatform');
      debugPrint('Using google-services.json for Client ID');

      // استخدام الإعداد الافتراضي بدون تحديد Client ID يدوياً
      // سيتم أخذ Client ID من google-services.json تلقائياً
      final GoogleSignIn googleSignIn = GoogleSignIn(
        scopes: ['email', 'profile'],
        signInOption: SignInOption.standard,
      );

      debugPrint('GoogleSignIn instance created');
      debugPrint('Using Client ID: ${googleSignIn.clientId}');
      debugPrint('Available accounts: ${await googleSignIn.isSignedIn()}');

      // Ensure we sign out first to force account selection
      await googleSignIn.signOut();
      debugPrint('Google Sign-In signOut completed to force account selection');

      // Start sign in flow
      debugPrint('Starting Google Sign In...');
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

      if (googleUser == null) {
        debugPrint('Google Sign In was cancelled by user');
        throw Exception('Google Sign In was cancelled');
      }

      debugPrint('Google User: ${googleUser.email}');

      // Get auth details
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Store tokens for use after async operations
      final String? idToken = googleAuth.idToken;
      final String? accessToken = googleAuth.accessToken;

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Call your backend API directly with Google tokens
      await loginCubit.firebaseLogin(
          idToken: idToken, accessToken: accessToken);

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // Get the state after the async operation
      final loginState = loginCubit.state;
      if (loginState.rStates == ResponseState.success) {
        CacheHelper.putString("token", loginState.loginModels!.token!);

        // Set and save the Google sign-in flag
        isGoogleSignIn = true;

        // Use a separate async function to ensure the value is saved
        _saveGoogleSignInFlag();

        // Verify Google sign-in flag is set correctly
        final isGoogle = CacheHelper.getBool("isGoogleSignIn") ?? false;
        debugPrint(
            "Google Login success - isGoogleSignIn from cache: $isGoogle");

        _onLoginSuccess(loginState);
      } else if (loginState.rStates == ResponseState.needVerivecation) {
        _onNeedVerification();
      }
    } catch (error, stackTrace) {
      debugPrint('=== Google Sign In Error ===');
      debugPrint('Error: $error');
      debugPrint('Stack trace: $stackTrace');

      // Check if widget is still mounted before showing error
      if (!mounted) return;

      // Show error message
      final errorMessage = _getSignInErrorMessage(error);

      // Use a local function to show the snackbar to avoid BuildContext issues
      void showErrorSnackBar() {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            backgroundColor: TColor.redAccent,
            content: CustomText(
              text: errorMessage,
              color: TColor.white,
            ),
          ),
        );
      }

      // Call the local function
      showErrorSnackBar();
    }
  }

  String _getSignInErrorMessage(dynamic error) {
    if (error.toString().contains('network_error')) {
      return AppStrings.checkConnection.tr();
    } else if (error.toString().contains('sign_in_canceled')) {
      return AppStrings.signInCanceled.tr();
    } else if (error.toString().contains('sign_in_failed')) {
      return AppStrings.signInFailed.tr();
    }
    return AppStrings.loginFailed.tr();
  }

  // Function to save Google sign-in flag with proper error handling
  Future<void> _saveGoogleSignInFlag() async {
    try {
      // Save the flag
      await CacheHelper.putBool("isGoogleSignIn", true);

      // Verify it was saved correctly
      final savedValue = CacheHelper.getBool("isGoogleSignIn");
      debugPrint("_saveGoogleSignInFlag - Saved value: $savedValue");

      if (savedValue != true) {
        // Try again if it wasn't saved correctly
        debugPrint(
            "_saveGoogleSignInFlag - Value not saved correctly, trying again");
        await CacheHelper.putBool("isGoogleSignIn", true);

        // Check again
        final secondCheck = CacheHelper.getBool("isGoogleSignIn");
        debugPrint("_saveGoogleSignInFlag - Second check: $secondCheck");
      }
    } catch (e) {
      debugPrint("_saveGoogleSignInFlag - Error: $e");
    }
  }
}
