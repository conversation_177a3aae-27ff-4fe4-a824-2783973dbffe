import 'package:bus/config/theme_colors.dart';
import 'package:bus/data/repository/temporary_address_repository.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class TemporaryAddressDetailsScreen extends StatefulWidget {
  final dynamic item; // طلب العنوان المؤقت

  const TemporaryAddressDetailsScreen({
    super.key,
    required this.item,
  });

  @override
  State<TemporaryAddressDetailsScreen> createState() =>
      _TemporaryAddressDetailsScreenState();
}

class _TemporaryAddressDetailsScreenState
    extends State<TemporaryAddressDetailsScreen> {
  // مجموعة العلامات للخريطة
  final Set<Marker> _markers = {};

  // موقع الكاميرا الأولي
  CameraPosition? _initialCameraPosition;

  @override
  void initState() {
    super.initState();
    _setupMapData();
  }

  // إعداد بيانات الخريطة
  void _setupMapData() {
    // التحقق من وجود إحداثيات
    if (widget.item.latitude != null && widget.item.longitude != null) {
      try {
        // تحويل الإحداثيات إلى أرقام
        final double lat = double.parse(widget.item.latitude);
        final double lng = double.parse(widget.item.longitude);

        // تعيين موقع الكاميرا الأولي
        _initialCameraPosition = CameraPosition(
          target: LatLng(lat, lng),
          zoom: 15,
        );

        // إضافة علامة للموقع
        _markers.add(
          Marker(
            markerId: const MarkerId('address_location'),
            position: LatLng(lat, lng),
            infoWindow: InfoWindow(
              title: widget.item.address ?? 'العنوان المؤقت',
            ),
          ),
        );
      } catch (e) {
        debugPrint('Error parsing coordinates: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحديد لون الحالة وأيقونتها
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (widget.item.acceptStatus) {
      case '0':
      case 'Processing':
        statusColor = Colors.orange;
        statusText = AppStrings.newS.tr();
        statusIcon = Icons.pending_outlined;
        break;
      case '1':
      case 'Approved':
        statusColor = Colors.green;
        statusText = AppStrings.accepted.tr();
        statusIcon = Icons.check_circle_outline;
        break;
      case '2':
      case 'Rejected':
        statusColor = Colors.red;
        statusText = AppStrings.refused.tr();
        statusIcon = Icons.cancel_outlined;
        break;
      default:
        statusColor = Colors.grey;
        statusText = widget.item.acceptStatus ?? '';
        statusIcon = Icons.help_outline;
    }

    // التحقق مما إذا كان الطلب جديدًا
    final bool isNewRequest = widget.item.acceptStatus == '0' ||
        widget.item.acceptStatus == 'Processing';

    // حساب الأيام المتبقية للعناوين المقبولة
    int remainingDays = 0;
    bool showRemainingDays = false;

    if (widget.item.acceptStatus == '1' ||
        widget.item.acceptStatus == 'Approved') {
      try {
        final DateTime endDate = DateTime.parse(widget.item.toDate);
        final DateTime today = DateTime.now();
        remainingDays = endDate.difference(today).inDays;
        showRemainingDays = remainingDays > 0;
      } catch (e) {
        // في حالة حدوث خطأ في تحليل التاريخ
      }
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: TColor.mainColor,
        elevation: 0,
        title: CustomText(
          text: AppStrings.newAddress.tr(),
          fontSize: 18,
          fontW: FontWeight.w600,
          color: Colors.white,
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          // عرض حالة الطلب في شريط العنوان
          Container(
            margin: EdgeInsets.only(left: 16.w, right: 16.w),
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 255, 255, 0.2),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  statusIcon,
                  size: 16,
                  color: Colors.white,
                ),
                SizedBox(width: 4.w),
                CustomText(
                  text: statusText,
                  fontSize: 14,
                  fontW: FontWeight.w500,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // شريط الحالة العلوي
          Container(
            height: 40.h,
            width: double.infinity,
            color: statusColor,
          ),

          // المحتوى الرئيسي
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // بطاقة المعلومات الرئيسية
                _buildMainInfoCard(
                    statusColor, statusText, showRemainingDays, remainingDays),

                // خريطة الموقع (إذا كانت الإحداثيات متوفرة)
                if (_initialCameraPosition != null) _buildMapCard(),

                // معلومات إضافية
                _buildAdditionalInfoCard(),

                // أزرار القبول والرفض (إذا كان الطلب جديدًا)
                if (isNewRequest) _buildActionButtons(),

                // مساحة إضافية في الأسفل
                SizedBox(height: 24.h),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بطاقة المعلومات الرئيسية
  Widget _buildMainInfoCard(Color statusColor, String statusText,
      bool showRemainingDays, int remainingDays) {
    return Card(
      margin: EdgeInsets.only(top: 24.h, left: 16.r, right: 16.r),
      elevation: 4,
      shadowColor: Colors.black38,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
        side: BorderSide(
          color: statusColor.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // شريط الحالة العلوي
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.15),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(widget.item.acceptStatus),
                  size: 22,
                  color: statusColor,
                ),
                SizedBox(width: 8.w),
                CustomText(
                  text: statusText,
                  fontSize: 16,
                  fontW: FontWeight.w600,
                  color: statusColor,
                ),
                const Spacer(),
                if (showRemainingDays)
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.timer_outlined,
                          size: 16,
                          color: remainingDays < 3
                              ? Colors.red.shade700
                              : Colors.green.shade700,
                        ),
                        SizedBox(width: 4.w),
                        CustomText(
                          text: remainingDays == 1
                              ? "يوم متبقي"
                              : "$remainingDays أيام",
                          fontSize: 14,
                          fontW: FontWeight.w500,
                          color: remainingDays < 3
                              ? Colors.red.shade700
                              : Colors.green.shade700,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          // العنوان
          Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان البطاقة
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: TColor.mainColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: const Icon(
                        Icons.location_on,
                        color: TColor.mainColor,
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    const Expanded(
                      child: CustomText(
                        text: "معلومات العنوان المؤقت",
                        fontSize: 18,
                        fontW: FontWeight.w700,
                        color: TColor.mainColor,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20.h),

                // العنوان الفعلي
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const CustomText(
                        text: "العنوان",
                        fontSize: 14,
                        fontW: FontWeight.w500,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 8.h),
                      CustomText(
                        text: widget.item.address ?? "عنوان غير معروف",
                        fontSize: 18,
                        fontW: FontWeight.w600,
                        color: TColor.black,
                        maxLine: 3,
                      ),
                    ],
                  ),
                ),

                // عرض الأيام المتبقية إذا كان الطلب مقبولاً
                if (showRemainingDays) ...[
                  SizedBox(height: 16.h),
                  Container(
                    width: double.infinity,
                    padding:
                        EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: remainingDays < 3
                            ? [Colors.red.shade50, Colors.red.shade100]
                            : [Colors.green.shade50, Colors.green.shade100],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16.r),
                      boxShadow: [
                        BoxShadow(
                          color: (remainingDays < 3 ? Colors.red : Colors.green)
                              .withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(10.r),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.timer_outlined,
                            size: 24,
                            color: remainingDays < 3
                                ? Colors.red.shade700
                                : Colors.green.shade700,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: "المدة المتبقية للعنوان المؤقت",
                                fontSize: 14,
                                fontW: FontWeight.w500,
                                color: remainingDays < 3
                                    ? Colors.red.shade700
                                    : Colors.green.shade700,
                              ),
                              SizedBox(height: 6.h),
                              CustomText(
                                text: remainingDays == 1
                                    ? "يوم واحد متبقي"
                                    : "$remainingDays أيام متبقية",
                                fontSize: 20,
                                fontW: FontWeight.w700,
                                color: remainingDays < 3
                                    ? Colors.red.shade800
                                    : Colors.green.shade800,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),

          // معلومات الفترة الزمنية
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(20.r),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
              border: Border(
                top: BorderSide(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(
                        Icons.date_range,
                        color: Colors.blue.shade700,
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    const CustomText(
                      text: "الفترة الزمنية",
                      fontSize: 16,
                      fontW: FontWeight.w600,
                      color: TColor.mainColor,
                    ),
                  ],
                ),

                SizedBox(height: 16.h),

                // تواريخ البداية والنهاية
                Container(
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      // تاريخ البداية
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  size: 16,
                                  color: Colors.blue.shade700,
                                ),
                                SizedBox(width: 6.w),
                                CustomText(
                                  text: AppStrings.startDate.tr(),
                                  fontSize: 14,
                                  fontW: FontWeight.w500,
                                  color: Colors.grey.shade700,
                                ),
                              ],
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  vertical: 8.h, horizontal: 12.w),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: CustomText(
                                text: _formatDate(widget.item.fromDate),
                                fontSize: 16,
                                fontW: FontWeight.w600,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // خط فاصل
                      Container(
                        height: 40.h,
                        width: 1,
                        color: Colors.grey.shade300,
                        margin: EdgeInsets.symmetric(horizontal: 12.w),
                      ),

                      // تاريخ النهاية
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.calendar_month,
                                  size: 16,
                                  color: Colors.purple.shade700,
                                ),
                                SizedBox(width: 6.w),
                                CustomText(
                                  text: AppStrings.endDate.tr(),
                                  fontSize: 14,
                                  fontW: FontWeight.w500,
                                  color: Colors.grey.shade700,
                                ),
                              ],
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  vertical: 8.h, horizontal: 12.w),
                              decoration: BoxDecoration(
                                color: Colors.purple.shade50,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: CustomText(
                                text: _formatDate(widget.item.toDate),
                                fontSize: 16,
                                fontW: FontWeight.w600,
                                color: Colors.purple.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على أيقونة الحالة
  IconData _getStatusIcon(String? status) {
    switch (status) {
      case '0':
      case 'Processing':
        return Icons.pending_outlined;
      case '1':
      case 'Approved':
        return Icons.check_circle_outline;
      case '2':
      case 'Rejected':
        return Icons.cancel_outlined;
      default:
        return Icons.help_outline;
    }
  }

  // بطاقة المعلومات الإضافية
  Widget _buildAdditionalInfoCard() {
    return Column(
      children: [
        // بطاقة معلومات الطلب
        Card(
          margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r),
          elevation: 4,
          shadowColor: Colors.black.withAlpha(40),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان البطاقة
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: Colors.teal.withAlpha(30),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(
                        Icons.info_outline,
                        color: Colors.teal.shade700,
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    const Expanded(
                      child: CustomText(
                        text: "معلومات الطلب",
                        fontSize: 18,
                        fontW: FontWeight.w700,
                        color: TColor.mainColor,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20.h),

                // تاريخ الإنشاء والتحديث
                Container(
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      // تاريخ الإنشاء
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(8.r),
                            decoration: BoxDecoration(
                              color: Colors.teal.withAlpha(20),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.access_time,
                              size: 18,
                              color: Colors.teal.shade700,
                            ),
                          ),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const CustomText(
                                  text: "تاريخ الإنشاء",
                                  fontSize: 14,
                                  fontW: FontWeight.w500,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 4.h),
                                CustomText(
                                  text: _formatDate(widget.item.createdAt),
                                  fontSize: 16,
                                  fontW: FontWeight.w600,
                                  color: TColor.black,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      // تاريخ التحديث (إذا كان مختلفاً عن تاريخ الإنشاء)
                      if (widget.item.updatedAt != null &&
                          widget.item.updatedAt != widget.item.createdAt) ...[
                        Divider(height: 24.h, color: Colors.grey.shade200),
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(8.r),
                              decoration: BoxDecoration(
                                color: Colors.amber.withAlpha(20),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.update,
                                size: 18,
                                color: Colors.amber.shade700,
                              ),
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const CustomText(
                                    text: "آخر تحديث",
                                    fontSize: 14,
                                    fontW: FontWeight.w500,
                                    color: Colors.grey,
                                  ),
                                  SizedBox(height: 4.h),
                                  CustomText(
                                    text: _formatDate(widget.item.updatedAt),
                                    fontSize: 16,
                                    fontW: FontWeight.w600,
                                    color: TColor.black,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        // بطاقة معلومات الطالب
        Card(
          margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r),
          elevation: 4,
          shadowColor: Colors.black.withAlpha(40),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade50, Colors.blue.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(20),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.person,
                        size: 24,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    const Expanded(
                      child: CustomText(
                        text: "معلومات الطالب",
                        fontSize: 18,
                        fontW: FontWeight.w700,
                        color: TColor.mainColor,
                      ),
                    ),
                  ],
                ),
              ),

              // محتوى البطاقة
              Padding(
                padding: EdgeInsets.all(20.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الطالب
                    _buildStudentInfoItem(
                      icon: Icons.badge,
                      iconColor: Colors.indigo.shade700,
                      iconBgColor: Colors.indigo.withAlpha(20),
                      title: "اسم الطالب",
                      value: _getStudentName(),
                    ),

                    // العنوان الأصلي للطالب
                    if (widget.item.student != null &&
                        widget.item.student.address != null) ...[
                      SizedBox(height: 16.h),
                      _buildStudentInfoItem(
                        icon: Icons.home,
                        iconColor: Colors.orange.shade700,
                        iconBgColor: Colors.orange.withAlpha(20),
                        title: "العنوان الأصلي",
                        value: widget.item.student.address ?? "غير محدد",
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),

        // بطاقة معلومات الحافلة
        Card(
          margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r),
          elevation: 4,
          shadowColor: Colors.black.withAlpha(40),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.amber.shade50, Colors.amber.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(20),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.directions_bus,
                        size: 24,
                        color: Colors.amber.shade700,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    const Expanded(
                      child: CustomText(
                        text: "معلومات الحافلة",
                        fontSize: 18,
                        fontW: FontWeight.w700,
                        color: TColor.mainColor,
                      ),
                    ),
                  ],
                ),
              ),

              // محتوى البطاقة
              Padding(
                padding: EdgeInsets.all(20.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الحافلة
                    _buildStudentInfoItem(
                      icon: Icons.directions_bus,
                      iconColor: Colors.red.shade700,
                      iconBgColor: Colors.red.withAlpha(20),
                      title: "اسم الحافلة",
                      value: _getBusNumber(),
                    ),

                    SizedBox(height: 16.h),

                    // اسم المشرف
                    _buildStudentInfoItem(
                      icon: Icons.person_pin,
                      iconColor: Colors.brown.shade700,
                      iconBgColor: Colors.brown.withAlpha(20),
                      title: "اسم المشرف",
                      value: _getDriverName(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // عنصر معلومات الطالب
  Widget _buildStudentInfoItem({
    required IconData icon,
    required Color iconColor,
    required Color iconBgColor,
    required String title,
    required String value,
    Widget? trailing,
  }) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.r),
            decoration: BoxDecoration(
              color: iconBgColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 20,
              color: iconColor,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: title,
                  fontSize: 14,
                  fontW: FontWeight.w500,
                  color: Colors.grey.shade600,
                ),
                SizedBox(height: 4.h),
                CustomText(
                  text: value,
                  fontSize: 16,
                  fontW: FontWeight.w600,
                  color: TColor.black,
                  maxLine: 2,
                ),
              ],
            ),
          ),
          if (trailing != null) trailing,
        ],
      ),
    );
  }

  // الاتصال بالسائق

  // بطاقة الخريطة
  Widget _buildMapCard() {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.r, vertical: 16.r),
      elevation: 4,
      shadowColor: Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade50, Colors.green.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.map,
                    size: 24,
                    color: Colors.green.shade700,
                  ),
                ),
                SizedBox(width: 12.w),
                const Expanded(
                  child: CustomText(
                    text: "موقع العنوان المؤقت",
                    fontSize: 18,
                    fontW: FontWeight.w700,
                    color: TColor.mainColor,
                  ),
                ),
              ],
            ),
          ),

          // الخريطة
          Container(
            height: 250.h,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.r),
                bottomRight: Radius.circular(20.r),
              ),
              child: Stack(
                children: [
                  // الخريطة
                  GoogleMap(
                    initialCameraPosition: _initialCameraPosition!,
                    markers: _markers,
                    mapType: MapType.normal,
                    onMapCreated: (controller) {
                      // لا نحتاج لحفظ المتحكم
                    },
                    zoomControlsEnabled: false,
                    myLocationEnabled: false,
                    myLocationButtonEnabled: false,
                  ),

                  // زر فتح الخريطة في تطبيق الخرائط
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: InkWell(
                      onTap: _openInMapsApp,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 10.h),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(30),
                              blurRadius: 6,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.open_in_new,
                              size: 16,
                              color: Colors.green.shade700,
                            ),
                            SizedBox(width: 6.w),
                            CustomText(
                              text: "فتح في الخرائط",
                              fontSize: 14,
                              fontW: FontWeight.w500,
                              color: Colors.green.shade700,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // فتح الموقع في تطبيق الخرائط
  void _openInMapsApp() async {
    if (widget.item.latitude == null || widget.item.longitude == null) return;

    try {
      final double lat = double.parse(widget.item.latitude);
      final double lng = double.parse(widget.item.longitude);

      final Uri uri = Uri.parse(
          'https://www.google.com/maps/search/?api=1&query=$lat,$lng');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("لا يمكن فتح تطبيق الخرائط"),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("حدث خطأ: ${e.toString()}"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // أزرار القبول والرفض
  Widget _buildActionButtons() {
    return Card(
      margin: EdgeInsets.only(top: 16.h, left: 16.r, right: 16.r, bottom: 24.h),
      elevation: 4,
      shadowColor: Colors.black.withAlpha(40),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البطاقة
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.deepPurple.shade50, Colors.deepPurple.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.rule,
                    size: 24,
                    color: Colors.deepPurple.shade700,
                  ),
                ),
                SizedBox(width: 12.w),
                const Expanded(
                  child: CustomText(
                    text: "إجراءات الطلب",
                    fontSize: 18,
                    fontW: FontWeight.w700,
                    color: TColor.mainColor,
                  ),
                ),
              ],
            ),
          ),

          // محتوى البطاقة
          Padding(
            padding: EdgeInsets.all(20.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // وصف الإجراءات
                Container(
                  padding: EdgeInsets.all(16.r),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8.r),
                        decoration: BoxDecoration(
                          color: Colors.blue.withAlpha(20),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.info_outline,
                          size: 20,
                          color: Colors.blue.shade700,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      const Expanded(
                        child: CustomText(
                          text:
                              "يمكنك قبول أو رفض طلب العنوان المؤقت. في حالة الرفض، يمكنك إضافة سبب الرفض.",
                          fontSize: 14,
                          fontW: FontWeight.w500,
                          color: Colors.grey,
                          maxLine: 3,
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24.h),

                // أزرار الإجراءات
                Row(
                  children: [
                    // زر الرفض
                    Expanded(
                      child: _buildActionButton(
                        onPressed: _showRejectDialog,
                        icon: Icons.close,
                        label: AppStrings.refusal.tr(),
                        backgroundColor: Colors.red.shade500,
                        gradient: LinearGradient(
                          colors: [Colors.red.shade400, Colors.red.shade600],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),

                    SizedBox(width: 16.w),

                    // زر القبول
                    Expanded(
                      child: _buildActionButton(
                        onPressed: _showAcceptDialog,
                        icon: Icons.check,
                        label: AppStrings.accept.tr(),
                        backgroundColor: Colors.green.shade500,
                        gradient: LinearGradient(
                          colors: [
                            Colors.green.shade400,
                            Colors.green.shade600
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // زر إجراء
  Widget _buildActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    required Color backgroundColor,
    required Gradient gradient,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withAlpha(60),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          elevation: 0,
        ),
        child: Ink(
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 8.w),
                CustomText(
                  text: label,
                  fontSize: 16,
                  fontW: FontWeight.w600,
                  color: Colors.white,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // تنسيق التاريخ
  String _formatDate(String? dateString) {
    if (dateString == null) return AppStrings.notSpecified.tr();
    try {
      final date = DateTime.parse(dateString);
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateString;
    }
  }

  // عرض مربع حوار القبول
  void _showAcceptDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 28,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: CustomText(
                text: AppStrings.confirmAcceptance.tr(),
                fontSize: 18,
                fontW: FontWeight.w600,
                color: TColor.mainColor,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const CustomText(
              text: "هل أنت متأكد من قبول طلب العنوان المؤقت؟",
              fontSize: 16,
              fontW: FontWeight.w400,
              maxLine: 3,
            ),
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: Colors.green.shade200,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Colors.green,
                    size: 20,
                  ),
                  SizedBox(width: 8.w),
                  const Expanded(
                    child: CustomText(
                      text:
                          "بعد القبول، سيتم إخطار الطالب بقبول طلب العنوان المؤقت.",
                      fontSize: 14,
                      fontW: FontWeight.w400,
                      color: Colors.green,
                      maxLine: 3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: const CustomText(
              text: "إلغاء",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // سيتم تنفيذ وظيفة القبول لاحقًا
              Navigator.pop(dialogContext); // إغلاق مربع الحوار

              _processAcceptRequest();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: const CustomText(
              text: "قبول",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // معالجة طلب القبول
  void _processAcceptRequest() async {
    // عرض مؤشر التقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (loadingContext) => const Center(
        child: CircularProgressIndicator(
          color: TColor.mainColor,
        ),
      ),
    );

    try {
      // استدعاء API لقبول الطلب
      final result =
          await TemporaryAddressRepository.respondToTemporaryAddressRequest(
        temporaryAddressId: widget.item.id!,
        acceptStatus: 1, // 1 للقبول
      );

      if (!mounted) return;

      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      if (result['status'] == true) {
        // عرض رسالة النجاح
        _showSuccessMessage(result['message'], Colors.green);

        // العودة إلى الشاشة السابقة
        Navigator.of(context).pop();
      } else {
        // عرض رسالة الخطأ
        _showSuccessMessage(result['message'], Colors.red);
      }
    } catch (e) {
      if (!mounted) return;

      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      // عرض رسالة الخطأ
      _showSuccessMessage(
          "حدث خطأ أثناء معالجة الطلب: ${e.toString()}", Colors.red);
    }
  }

  // عرض رسالة نجاح
  void _showSuccessMessage(String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              backgroundColor == Colors.green
                  ? Icons.check_circle
                  : Icons.cancel,
              color: Colors.white,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(message),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }

  // عرض مربع حوار الرفض
  void _showRejectDialog() {
    final TextEditingController reasonController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.cancel,
              color: Colors.red,
              size: 28,
            ),
            SizedBox(width: 8.w),
            const Expanded(
              child: CustomText(
                text: "تأكيد الرفض",
                fontSize: 18,
                fontW: FontWeight.w600,
                color: TColor.mainColor,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: AppStrings.confirmRejectTempAddress.tr(),
              fontSize: 16,
              fontW: FontWeight.w400,
              maxLine: 3,
            ),
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: Colors.amber.shade200,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Colors.amber,
                    size: 20,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: CustomText(
                      text: AppStrings.rejectReasonSuggestion.tr(),
                      fontSize: 14,
                      fontW: FontWeight.w400,
                      color: Colors.amber,
                      maxLine: 3,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            const CustomText(
              text: "سبب الرفض (اختياري):",
              fontSize: 14,
              fontW: FontWeight.w500,
            ),
            SizedBox(height: 8.h),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                hintText: "أدخل سبب الرفض هنا...",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                filled: true,
                fillColor: Colors.grey.shade50,
                hintStyle: TextStyle(
                  color: Colors.grey.shade400,
                  fontSize: 14,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: const BorderSide(
                    color: TColor.mainColor,
                    width: 1.5,
                  ),
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            ),
            child: const CustomText(
              text: "إلغاء",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // سيتم تنفيذ وظيفة الرفض لاحقًا
              final String reason = reasonController.text;
              Navigator.pop(dialogContext); // إغلاق مربع الحوار

              _processRejectRequest(reason);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: const CustomText(
              text: "رفض",
              fontSize: 16,
              fontW: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // معالجة طلب الرفض
  void _processRejectRequest(String reason) async {
    // عرض مؤشر التقدم
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (loadingContext) => const Center(
        child: CircularProgressIndicator(
          color: TColor.mainColor,
        ),
      ),
    );

    try {
      // استدعاء API لرفض الطلب
      final result =
          await TemporaryAddressRepository.respondToTemporaryAddressRequest(
        temporaryAddressId: widget.item.id!,
        acceptStatus: 2, // 2 للرفض
      );

      if (!mounted) return;

      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      if (result['status'] == true) {
        // عرض رسالة النجاح مع سبب الرفض إذا كان موجودًا
        _showSuccessMessage(
            reason.isNotEmpty
                ? "${result['message']} بسبب: $reason"
                : result['message'],
            Colors.red);

        // العودة إلى الشاشة السابقة
        Navigator.of(context).pop();
      } else {
        // عرض رسالة الخطأ
        _showSuccessMessage(result['message'], Colors.red);
      }
    } catch (e) {
      if (!mounted) return;

      // إغلاق مؤشر التقدم
      Navigator.of(context).pop();

      // عرض رسالة الخطأ
      _showSuccessMessage(
          "حدث خطأ أثناء معالجة الطلب: ${e.toString()}", Colors.red);
    }
  }

  // الحصول على اسم الطالب
  String _getStudentName() {
    try {
      // محاولة الحصول على اسم الطالب من الهيكل الجديد
      if (widget.item.student != null) {
        return widget.item.student.name ?? "غير محدد";
      }
      // للتوافق مع الهيكل القديم
      return widget.item.student_name ?? "غير محدد";
    } catch (e) {
      // في حالة حدوث خطأ
      return "غير محدد";
    }
  }

  // الحصول على رقم الحافلة
  String _getBusNumber() {
    try {
      // محاولة الحصول على رقم الحافلة من الهيكل الجديد
      if (widget.item.student != null && widget.item.student.bus != null) {
        return widget.item.student.bus.name ?? "غير محدد";
      }

      // للتوافق مع الهيكل القديم
      return widget.item.bus_number?.toString() ?? "غير محدد";
    } catch (e) {
      // في حالة حدوث خطأ
      return "غير محدد";
    }
  }

  // الحصول على اسم السائق
  String _getDriverName() {
    try {
      // محاولة الحصول على اسم السائق من الهيكل الجديد
      if (widget.item.student != null &&
          widget.item.student.bus != null &&
          widget.item.student.bus.admin != null) {
        return widget.item.student.bus.admin.name ?? "غير محدد";
      }

      // للتوافق مع الهيكل القديم
      return widget.item.driver_name ?? "غير محدد";
    } catch (e) {
      // في حالة حدوث خطأ
      return "غير محدد";
    }
  }
}
