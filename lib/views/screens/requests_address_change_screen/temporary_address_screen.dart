import 'package:bus/bloc/temporary_address_cubit/temporary_address_cubit.dart';
import 'package:bus/bloc/temporary_address_cubit/temporary_address_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/requests_address_change_screen/temporary_address_details_screen.dart';
import 'package:bus/widgets/page_number_widget.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TemporaryAddressScreen extends StatefulWidget {
  final String? status;
  final String? appBarTitle;

  const TemporaryAddressScreen({
    super.key,
    this.status,
    this.appBarTitle,
  });

  @override
  State<TemporaryAddressScreen> createState() => _TemporaryAddressScreenState();
}

class _TemporaryAddressScreenState extends State<TemporaryAddressScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) =>
          TemporaryAddressCubit()..getTemporaryAddresses(status: widget.status),
      child: BlocBuilder<TemporaryAddressCubit, TemporaryAddressStates>(
        builder: (context, state) {
          if (state is TemporaryAddressLoadingState) {
            return _buildLoadingState();
          } else if (state is TemporaryAddressSuccessState) {
            if (state.temporaryAddressItems == null ||
                state.temporaryAddressItems!.isEmpty) {
              return _buildEmptyState();
            } else {
              return _buildSuccessState(state);
            }
          } else if (state is TemporaryAddressErrorState) {
            return _buildErrorState(state.error);
          } else {
            return const SizedBox();
          }
        },
      ),
    );
  }

  // حالة التحميل
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: TColor.mainColor,
          ),
          const SizedBox(height: 16),
          CustomText(
            text: AppStrings.loadingRequests.tr(),
            fontSize: 16,
            fontW: FontWeight.w500,
            color: TColor.mainColor,
          ),
        ],
      ),
    );
  }

  // حالة عدم وجود طلبات
  Widget _buildEmptyState() {
    return SizedBox(
      width: double.infinity,
      height: 300.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off,
              size: 64,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            CustomText(
              text: AppStrings.requestAbsencesNotFound.tr(),
              fontSize: 18,
              fontW: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
            SizedBox(height: 8.h),
            CustomText(
              text: "لا توجد طلبات عناوين مؤقتة حالياً",
              fontSize: 14,
              fontW: FontWeight.w400,
              color: Colors.grey.shade500,
            ),
          ],
        ),
      ),
    );
  }

  // حالة الخطأ
  Widget _buildErrorState(String error) {
    return SizedBox(
      width: double.infinity,
      height: 300.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            SizedBox(height: 16.h),
            CustomText(
              text: "حدث خطأ أثناء تحميل البيانات",
              fontSize: 18,
              fontW: FontWeight.w600,
              color: Colors.red.shade700,
            ),
            SizedBox(height: 8.h),
            CustomText(
              text: error,
              fontSize: 14,
              fontW: FontWeight.w400,
              color: Colors.grey.shade700,
            ),
          ],
        ),
      ),
    );
  }

  // حالة النجاح
  Widget _buildSuccessState(TemporaryAddressSuccessState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: const CustomText(
            text: "طلبات العناوين المؤقتة",
            fontSize: 18,
            fontW: FontWeight.w600,
            color: TColor.mainColor,
          ),
        ),

        // معلومات إضافية
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            children: [
              CustomText(
                text: "عدد الطلبات: ${state.temporaryAddressItems!.length}",
                fontSize: 14,
                fontW: FontWeight.w500,
                color: Colors.grey.shade700,
              ),
              const Spacer(),
              _buildStatusIndicator(widget.status),
            ],
          ),
        ),

        SizedBox(height: 16.h),

        // قائمة الطلبات
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: state.temporaryAddressItems!.length,
            itemBuilder: (context, index) {
              final item = state.temporaryAddressItems![index];
              return _buildAddressCard(item);
            },
          ),
        ),

        // ترقيم الصفحات
        Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: PageNumberWidget(
            lastPage: state.lastPage,
            currentPage: state.currentPage,
            type: "TemporaryAddress",
            status: widget.status,
          ),
        ),
      ],
    );
  }

  // بطاقة عنوان
  Widget _buildAddressCard(dynamic item) {
    // تنسيق التواريخ
    String fromDate = _formatDate(item.fromDate);
    String toDate = _formatDate(item.toDate);

    // الحصول على معلومات الطالب من الهيكل الجديد
    if (item.student != null) {
      if (item.student.bus != null) {
        // معلومات الحافلة متوفرة
      }
    }

    // تحديد لون الحالة
    Color statusColor;
    IconData statusIcon;
    switch (item.acceptStatus) {
      case '0':
      case 'Processing':
        statusColor = Colors.orange;
        statusIcon = Icons.pending_outlined;
        break;
      case '1':
      case 'Approved':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle_outline;
        break;
      case '2':
      case 'Rejected':
        statusColor = Colors.red;
        statusIcon = Icons.cancel_outlined;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help_outline;
    }

    // حساب الأيام المتبقية
    int remainingDays = 0;
    try {
      final DateTime endDate = DateTime.parse(item.toDate);
      final DateTime today = DateTime.now();
      remainingDays = endDate.difference(today).inDays;
    } catch (e) {
      // في حالة حدوث خطأ في تحليل التاريخ
    }

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 3,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
        side: BorderSide(
          color: Color.fromRGBO(statusColor.r.toInt(), statusColor.g.toInt(),
              statusColor.b.toInt(), 0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _navigateToDetailsScreen(context, item),
        borderRadius: BorderRadius.circular(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // شريط الحالة العلوي
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 8.h),
              decoration: BoxDecoration(
                color: Color.fromRGBO(statusColor.r.toInt(),
                    statusColor.g.toInt(), statusColor.b.toInt(), 0.15),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    statusIcon,
                    size: 18,
                    color: statusColor,
                  ),
                  SizedBox(width: 6.w),
                  CustomText(
                    text: _getStatusText(item.acceptStatus),
                    fontSize: 14,
                    fontW: FontWeight.w600,
                    color: statusColor,
                  ),
                ],
              ),
            ),

            // محتوى البطاقة
            Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 22,
                        color: TColor.mainColor,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: CustomText(
                          text: item.address ?? "عنوان غير معروف",
                          fontSize: 16,
                          fontW: FontWeight.w600,
                          color: TColor.black,
                          maxLine: 2,
                        ),
                      ),
                    ],
                  ),

                  // معلومات الطالب
                  if (item.student != null) ...[
                    SizedBox(height: 12.h),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.person,
                          size: 22,
                          color: TColor.mainColor,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: CustomText(
                            text: "الطالب: ${item.student.name ?? "غير معروف"}",
                            fontSize: 14,
                            fontW: FontWeight.w500,
                            color: Colors.grey.shade800,
                            maxLine: 2,
                          ),
                        ),
                      ],
                    ),
                  ],

                  // معلومات الحافلة
                  if (item.student != null && item.student.bus != null) ...[
                    SizedBox(height: 8.h),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(
                          Icons.directions_bus,
                          size: 22,
                          color: TColor.mainColor,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: CustomText(
                            text:
                                "الحافلة: ${item.student.bus.name ?? "غير معروف"}",
                            fontSize: 14,
                            fontW: FontWeight.w500,
                            color: Colors.grey.shade800,
                            maxLine: 2,
                          ),
                        ),
                      ],
                    ),
                  ],

                  SizedBox(height: 16.h),

                  // معلومات الفترة الزمنية
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            _buildInfoItem(
                              Icons.calendar_today,
                              "من: $fromDate",
                              flex: 1,
                              iconColor: Colors.blue.shade700,
                            ),
                            _buildInfoItem(
                              Icons.calendar_month,
                              "إلى: $toDate",
                              flex: 1,
                              iconColor: Colors.purple.shade700,
                            ),
                          ],
                        ),
                        if (remainingDays > 0 && item.acceptStatus == '1') ...[
                          Divider(height: 16.h),
                          Row(
                            children: [
                              Icon(
                                Icons.timer_outlined,
                                size: 16,
                                color: remainingDays < 3
                                    ? Colors.red.shade700
                                    : Colors.green.shade700,
                              ),
                              SizedBox(width: 4.w),
                              CustomText(
                                text: remainingDays == 1
                                    ? "يوم واحد متبقي"
                                    : "$remainingDays أيام متبقية",
                                fontSize: 14,
                                fontW: FontWeight.w500,
                                color: remainingDays < 3
                                    ? Colors.red.shade700
                                    : Colors.green.shade700,
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // شريط الإجراءات السفلي
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16.r),
                  bottomRight: Radius.circular(16.r),
                ),
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // تاريخ الإنشاء
                  Expanded(
                    child: Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 14,
                            color: Colors.grey.shade600,
                          ),
                          SizedBox(width: 4.w),
                          Expanded(
                            child: CustomText(
                              text:
                                  "تم الإنشاء: ${_formatDate(item.createdAt)}",
                              fontSize: 12,
                              fontW: FontWeight.w400,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // زر التفاصيل
                  Container(
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                    ),
                    child: TextButton.icon(
                      onPressed: () => _navigateToDetailsScreen(context, item),
                      icon: const Icon(Icons.arrow_forward_ios, size: 14),
                      label: const CustomText(
                        text: "التفاصيل",
                        fontSize: 14,
                        fontW: FontWeight.w500,
                      ),
                      style: TextButton.styleFrom(
                        foregroundColor: TColor.mainColor,
                        padding: EdgeInsets.symmetric(
                            horizontal: 16.w, vertical: 12.h),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.zero,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عنصر معلومات
  Widget _buildInfoItem(IconData icon, String text,
      {int flex = 1, Color? iconColor}) {
    return Expanded(
      flex: flex,
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: iconColor ?? Colors.grey.shade600,
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: CustomText(
              text: text,
              fontSize: 14,
              fontW: FontWeight.w400,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // مؤشر الحالة
  Widget _buildStatusIndicator(String? status) {
    String statusText;
    Color statusColor;

    switch (status) {
      case 'new':
        statusText = "طلبات جديدة";
        statusColor = Colors.orange;
        break;
      case 'accept':
        statusText = "طلبات مقبولة";
        statusColor = Colors.green;
        break;
      case 'unaccept':
        statusText = "طلبات مرفوضة";
        statusColor = Colors.red;
        break;
      default:
        statusText = "جميع الطلبات";
        statusColor = TColor.mainColor;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: Color.fromRGBO(statusColor.r.toInt(), statusColor.g.toInt(),
            statusColor.b.toInt(), 0.1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 6.w),
          CustomText(
            text: statusText,
            fontSize: 12,
            fontW: FontWeight.w500,
            color: statusColor,
          ),
        ],
      ),
    );
  }

  // تنسيق التاريخ
  String _formatDate(String? dateString) {
    if (dateString == null) return '';
    try {
      final date = DateTime.parse(dateString);
      return date.toString().substring(0, 10); // yyyy-MM-dd format
    } catch (e) {
      return dateString;
    }
  }

  // الانتقال إلى صفحة التفاصيل
  void _navigateToDetailsScreen(BuildContext context, dynamic item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TemporaryAddressDetailsScreen(
          item: item,
        ),
      ),
    );
  }

  // الحصول على نص الحالة
  String _getStatusText(String? status) {
    if (status == null) return '';

    switch (status) {
      case '0':
      case 'Processing':
        return AppStrings.newS.tr();
      case '1':
      case 'Approved':
        return AppStrings.accepted.tr();
      case '2':
      case 'Rejected':
        return AppStrings.refused.tr();
      default:
        return status;
    }
  }
}
