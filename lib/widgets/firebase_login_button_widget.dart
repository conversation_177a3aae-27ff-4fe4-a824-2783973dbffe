import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_cubit.dart';
import 'package:bus/bloc/firebase_login_status_cubit/firebase_login_status_states.dart';
import 'package:bus/bloc/login_cubit/login_cubit.dart';
import 'package:bus/helper/response_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:logger/logger.dart';

/// Widget that conditionally shows Google Sign-In button based on Firebase login status
/// This widget demonstrates how to use the Firebase login status API
class FirebaseLoginButtonWidget extends StatefulWidget {
  /// Callback when login is successful
  final VoidCallback? onLoginSuccess;
  
  /// Callback when login fails
  final Function(String error)? onLoginError;
  
  /// Custom button style
  final ButtonStyle? buttonStyle;
  
  /// Custom button text
  final String? buttonText;

  const FirebaseLoginButtonWidget({
    Key? key,
    this.onLoginSuccess,
    this.onLoginError,
    this.buttonStyle,
    this.buttonText,
  }) : super(key: key);

  @override
  State<FirebaseLoginButtonWidget> createState() => _FirebaseLoginButtonWidgetState();
}

class _FirebaseLoginButtonWidgetState extends State<FirebaseLoginButtonWidget> {
  @override
  void initState() {
    super.initState();
    // Check Firebase login availability when widget initializes
    _checkFirebaseLoginAvailability();
  }

  /// Check if Firebase login is available/enabled
  void _checkFirebaseLoginAvailability() {
    context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginAvailability();
  }

  /// Handle Google Sign-In process
  Future<void> _handleGoogleSignIn() async {
    try {
      debugPrint('Starting Google Sign In process');
      
      final GoogleSignIn googleSignIn = GoogleSignIn(
        scopes: ['email', 'profile'],
        signInOption: SignInOption.standard,
      );

      // Sign out first to ensure clean state
      await googleSignIn.signOut();
      
      // Perform Google Sign-In
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
      
      if (googleUser == null) {
        debugPrint('Google Sign In cancelled by user');
        widget.onLoginError?.call('Google Sign In cancelled');
        return;
      }

      debugPrint('Google Sign In successful for: ${googleUser.email}');
      
      // Get authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      if (googleAuth.idToken == null) {
        debugPrint('Failed to get Google ID token');
        widget.onLoginError?.call('Failed to get authentication token');
        return;
      }

      // Store tokens for use after async operations
      final String? idToken = googleAuth.idToken;
      final String? accessToken = googleAuth.accessToken;

      // Check if widget is still mounted before proceeding
      if (!mounted) return;

      // First, check Firebase login status with the obtained token
      await context.read<FirebaseLoginStatusCubit>().checkFirebaseLoginStatus(
        idToken: idToken,
        accessToken: accessToken,
      );

      // Then proceed with actual login
      await context.read<LoginCubit>().firebaseLogin(
        idToken: idToken,
        accessToken: accessToken,
      );

    } catch (e) {
      debugPrint('Google Sign In error: $e');
      Logger().e('Google Sign In error: $e');
      widget.onLoginError?.call('Google Sign In failed: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to Firebase login status changes
        BlocListener<FirebaseLoginStatusCubit, FirebaseLoginStatusState>(
          listener: (context, state) {
            if (state.rStates == ResponseState.failure) {
              debugPrint('Firebase login status check failed: ${state.message}');
              widget.onLoginError?.call(state.message ?? 'Failed to check login status');
            }
          },
        ),
        // Listen to actual login changes
        BlocListener<LoginCubit, dynamic>(
          listener: (context, state) {
            if (state.rStates == ResponseState.success) {
              debugPrint('Firebase login successful');
              widget.onLoginSuccess?.call();
            } else if (state.rStates == ResponseState.failure) {
              debugPrint('Firebase login failed');
              widget.onLoginError?.call('Login failed');
            }
          },
        ),
      ],
      child: BlocBuilder<FirebaseLoginStatusCubit, FirebaseLoginStatusState>(
        builder: (context, firebaseStatusState) {
          // Show loading indicator while checking status
          if (firebaseStatusState.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // Hide button if Firebase login is not available or parent is already logged in
          if (firebaseStatusState.isFailure || 
              (firebaseStatusState.hasValidData && firebaseStatusState.isParentLoggedIn)) {
            return const SizedBox.shrink();
          }

          // Show Google Sign-In button if Firebase login is available and parent is not logged in
          if (firebaseStatusState.hasValidData && firebaseStatusState.isParentNotLoggedIn) {
            return BlocBuilder<LoginCubit, dynamic>(
              builder: (context, loginState) {
                final bool isLoggingIn = loginState.rStates == ResponseState.loading;
                
                return ElevatedButton.icon(
                  onPressed: isLoggingIn ? null : _handleGoogleSignIn,
                  style: widget.buttonStyle ?? ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.black87,
                    elevation: 2,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: const BorderSide(color: Colors.grey, width: 1),
                    ),
                  ),
                  icon: isLoggingIn 
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Image.asset(
                        'assets/images/google_logo.png', // Add Google logo asset
                        height: 20,
                        width: 20,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.login, size: 20);
                        },
                      ),
                  label: Text(
                    isLoggingIn 
                      ? 'Signing in...' 
                      : (widget.buttonText ?? 'Sign in with Google'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              },
            );
          }

          // Default case - show nothing
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
