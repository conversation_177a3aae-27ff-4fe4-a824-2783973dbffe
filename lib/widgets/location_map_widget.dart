import 'package:bus/config/theme_colors.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/full_map_screen/full_map_screen.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class LocationMapWidget extends StatefulWidget {
  final String? latitude;
  final String? longitude;
  final double height;
  final double width;
  final double borderRadius;

  const LocationMapWidget({
    super.key,
    this.latitude,
    this.longitude,
    this.height = 150,
    this.width = double.infinity,
    this.borderRadius = 10,
  });

  @override
  State<LocationMapWidget> createState() => _LocationMapWidgetState();
}

class _LocationMapWidgetState extends State<LocationMapWidget> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    _initializeMarkers();
  }

  void _initializeMarkers() {
    if (widget.latitude != null && widget.longitude != null) {
      try {
        final double lat = double.parse(widget.latitude!);
        final double lng = double.parse(widget.longitude!);

        _markers.add(
          Marker(
            markerId: const MarkerId('student_location'),
            position: LatLng(lat, lng),
          ),
        );
      } catch (e) {
        // Handle parsing error
        debugPrint('Error parsing coordinates: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if we have valid coordinates
    bool hasValidCoordinates = false;
    LatLng? position;

    debugPrint('=== LocationMapWidget Debug ===');
    debugPrint('Latitude: ${widget.latitude}');
    debugPrint('Longitude: ${widget.longitude}');

    if (widget.latitude != null && widget.longitude != null) {
      try {
        final double lat = double.parse(widget.latitude!);
        final double lng = double.parse(widget.longitude!);
        position = LatLng(lat, lng);
        hasValidCoordinates = true;
        debugPrint('Valid coordinates: $lat, $lng');
      } catch (e) {
        // Handle parsing error
        debugPrint('Error parsing coordinates: $e');
      }
    } else {
      debugPrint('Latitude or Longitude is null');
    }

    debugPrint('hasValidCoordinates: $hasValidCoordinates');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: AppStrings.studentLocation.tr(),
          fontSize: 15,
          fontW: FontWeight.w500,
          color: TColor.namePersonal,
        ),
        SizedBox(height: 5.h),
        Container(
          height: widget.height.h,
          width: widget.width.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius.r),
            border: Border.all(color: TColor.dialogName, width: 1.w),
          ),
          clipBehavior: Clip.antiAlias,
          child: hasValidCoordinates
              ? GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: position!,
                    zoom: 15,
                  ),
                  markers: _markers,
                  zoomControlsEnabled: false,
                  mapToolbarEnabled: false,
                  myLocationButtonEnabled: false,
                  onMapCreated: (GoogleMapController controller) {
                    _mapController = controller;
                  },
                )
              : Center(
                  child: CustomText(
                    text: AppStrings.locationNotAvailable.tr(),
                    fontSize: 14,
                    color: TColor.grey5,
                  ),
                ),
        ),
        if (hasValidCoordinates)
          Padding(
            padding: EdgeInsets.only(top: 5.h),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomText(
                      text: "${AppStrings.latitude.tr()}: ${widget.latitude}",
                      fontSize: 12,
                      color: TColor.grey5,
                    ),
                    SizedBox(width: 10.w),
                    CustomText(
                      text: "${AppStrings.longitude.tr()}: ${widget.longitude}",
                      fontSize: 12,
                      color: TColor.grey5,
                    ),
                  ],
                ),
                SizedBox(height: 10.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        // Open full map view
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => FullMapScreen(
                              latitude: widget.latitude,
                              longitude: widget.longitude,
                            ),
                          ),
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          color: TColor.mainColor,
                          borderRadius: BorderRadius.circular(5.r),
                        ),
                        child: CustomText(
                          text: AppStrings.showFullMap.tr(),
                          fontSize: 12,
                          color: TColor.white,
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    InkWell(
                      onTap: () async {
                        // Open in external maps app
                        final url =
                            'https://www.google.com/maps/search/?api=1&query=${widget.latitude},${widget.longitude}';
                        final Uri uri = Uri.parse(url);
                        if (await canLaunchUrl(uri)) {
                          await launchUrl(uri,
                              mode: LaunchMode.externalApplication);
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          color: TColor.fillFormFieldB,
                          borderRadius: BorderRadius.circular(5.r),
                        ),
                        child: CustomText(
                          text: AppStrings.openInMaps.tr(),
                          fontSize: 12,
                          color: TColor.grey5,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}
