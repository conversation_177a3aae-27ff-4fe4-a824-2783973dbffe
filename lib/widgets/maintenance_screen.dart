import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../config/theme_colors.dart';

class MaintenanceScreen extends StatelessWidget {
  final String message;
  final String appName;
  final VoidCallback? onRetry;

  const MaintenanceScreen({
    Key? key,
    required this.message,
    required this.appName,
    this.onRetry,
  }) : super(key: key);

  // Helper method for localization
  String _getLocalizedText(
      BuildContext context, String arabic, String english) {
    return context.locale.toString() == "ar" ? arabic : english;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: TColor.white,
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                TColor.primary.withValues(alpha: 0.05),
                TColor.white,
                TColor.primary.withValues(alpha: 0.02),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height - 48,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App Logo/Icon
                    Container(
                      width: 140,
                      height: 140,
                      decoration: BoxDecoration(
                        color: TColor.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(70),
                        boxShadow: [
                          BoxShadow(
                            color: TColor.primary.withValues(alpha: 0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.directions_bus_rounded,
                        size: 70,
                        color: TColor.primary,
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Title
                    Text(
                      _getLocalizedText(
                          context, 'تحت الصيانة', 'Under Maintenance'),
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: TColor.text,
                        height: 1.2,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // App Name with Busaty branding
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 8),
                      decoration: BoxDecoration(
                        color: TColor.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: TColor.primary.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        _getLocalizedText(context, 'Busaty - المدارس',
                            'Busaty - Schools'),
                        style: TextStyle(
                          color: TColor.primary,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Message Card
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: TColor.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: TColor.grey5.withValues(alpha: 0.3),
                            blurRadius: 15,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.info_outline_rounded,
                            color: TColor.primary,
                            size: 32,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            message.isNotEmpty
                                ? message
                                : _getLocalizedText(
                                    context,
                                    'التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
                                    'The app is currently being updated. Please try again later.',
                                  ),
                            style: TextStyle(
                              color: TColor.text,
                              fontSize: 16,
                              height: 1.6,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Retry Button
                    if (onRetry != null)
                      Container(
                        width: double.infinity,
                        height: 50,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [TColor.primary, TColor.mainColor],
                          ),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: TColor.primary.withValues(alpha: 0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: onRetry,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          child: Text(
                            _getLocalizedText(
                                context, 'إعادة المحاولة', 'Try Again'),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),

                    const SizedBox(height: 30),

                    // Loading indicator with custom colors
                    SizedBox(
                      width: 30,
                      height: 30,
                      child: CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(TColor.primary),
                        strokeWidth: 3,
                      ),
                    ),

                    const SizedBox(height: 16),

                    Text(
                      _getLocalizedText(
                          context, 'جاري التحقق...', 'Checking...'),
                      style: TextStyle(
                        color: TColor.grey5,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
