import 'dart:convert';
import 'dart:io';

/// <PERSON>ript to check Google Sign-In configuration
/// Run with: dart run scripts/check_google_signin_config.dart
void main() async {
  print('🔍 Checking Google Sign-In Configuration...\n');

  // Check google-services.json
  await checkGoogleServicesJson();
  
  // Check key.properties
  await checkKeyProperties();
  
  // Check build.gradle
  await checkBuildGradle();
  
  print('\n✅ Configuration check completed!');
  print('\n📋 Next Steps:');
  print('1. Verify SHA-1 fingerprints in Firebase Console');
  print('2. Download updated google-services.json if needed');
  print('3. Check OAuth Consent Screen in Google Cloud Console');
  print('4. Test with flutter build appbundle --release');
}

Future<void> checkGoogleServicesJson() async {
  print('📱 Checking android/app/google-services.json...');
  
  final file = File('android/app/google-services.json');
  if (!file.existsSync()) {
    print('❌ google-services.json not found!');
    return;
  }
  
  try {
    final content = await file.readAsString();
    final json = jsonDecode(content);
    
    // Find com.busaty.school client
    final clients = json['client'] as List;
    final schoolClient = clients.firstWhere(
      (client) => client['client_info']['android_client_info']['package_name'] == 'com.busaty.school',
      orElse: () => null,
    );
    
    if (schoolClient == null) {
      print('❌ com.busaty.school not found in google-services.json');
      return;
    }
    
    print('✅ Found com.busaty.school configuration');
    
    // Check OAuth clients
    final oauthClients = schoolClient['oauth_client'] as List;
    final androidClients = oauthClients.where((client) => client['client_type'] == 1).toList();
    
    print('📋 Found ${androidClients.length} Android OAuth clients:');
    for (final client in androidClients) {
      final hash = client['android_info']['certificate_hash'];
      final clientId = client['client_id'];
      print('   • SHA-1: $hash');
      print('     Client ID: $clientId');
    }
    
    // Check for expected SHA-1 fingerprints
    final expectedHashes = [
      '8ac73c0cffc21c28038497fb8e33f37b1d55d523', // Debug
      'a81efc57f5b0311996396040bdfea37ccd04cd8e', // Release
      '3ceda50a3b31262b0dc1af26b85ec284da66b467', // Play Store
    ];
    
    print('\n🔍 Checking for expected SHA-1 fingerprints:');
    for (final expectedHash in expectedHashes) {
      final found = androidClients.any((client) => 
        client['android_info']['certificate_hash'] == expectedHash);
      final type = expectedHash == '8ac73c0cffc21c28038497fb8e33f37b1d55d523' ? 'Debug' :
                   expectedHash == 'a81efc57f5b0311996396040bdfea37ccd04cd8e' ? 'Release' : 'Play Store';
      print('   ${found ? '✅' : '❌'} $type: $expectedHash');
    }
    
  } catch (e) {
    print('❌ Error reading google-services.json: $e');
  }
}

Future<void> checkKeyProperties() async {
  print('\n🔑 Checking android/key.properties...');
  
  final file = File('android/key.properties');
  if (!file.existsSync()) {
    print('❌ key.properties not found!');
    return;
  }
  
  try {
    final content = await file.readAsString();
    final lines = content.split('\n');
    
    String? keyAlias;
    String? storeFile;
    
    for (final line in lines) {
      if (line.startsWith('keyAlias=')) {
        keyAlias = line.split('=')[1];
      } else if (line.startsWith('storeFile=')) {
        storeFile = line.split('=')[1];
      }
    }
    
    print('✅ Key properties found:');
    print('   • Key Alias: $keyAlias');
    print('   • Store File: $storeFile');
    
    // Check if keystore file exists
    if (storeFile != null) {
      final keystoreFile = File(storeFile);
      if (keystoreFile.existsSync()) {
        print('   ✅ Keystore file exists');
      } else {
        print('   ❌ Keystore file not found: $storeFile');
      }
    }
    
  } catch (e) {
    print('❌ Error reading key.properties: $e');
  }
}

Future<void> checkBuildGradle() async {
  print('\n🏗️ Checking android/app/build.gradle...');
  
  final file = File('android/app/build.gradle');
  if (!file.existsSync()) {
    print('❌ build.gradle not found!');
    return;
  }
  
  try {
    final content = await file.readAsString();
    
    // Check for Google Services plugin
    if (content.contains("id 'com.google.gms.google-services'")) {
      print('✅ Google Services plugin found');
    } else {
      print('❌ Google Services plugin not found');
    }
    
    // Check for signing config
    if (content.contains('signingConfigs')) {
      print('✅ Signing configuration found');
    } else {
      print('❌ Signing configuration not found');
    }
    
    // Check application ID
    final appIdMatch = RegExp(r'applicationId\s+"([^"]+)"').firstMatch(content);
    if (appIdMatch != null) {
      final appId = appIdMatch.group(1);
      print('✅ Application ID: $appId');
      if (appId != 'com.busaty.school') {
        print('⚠️  Warning: Application ID does not match expected com.busaty.school');
      }
    } else {
      print('❌ Application ID not found');
    }
    
  } catch (e) {
    print('❌ Error reading build.gradle: $e');
  }
}
