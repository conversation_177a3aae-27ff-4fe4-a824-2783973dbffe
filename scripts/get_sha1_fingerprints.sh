#!/bin/bash

# Script to get SHA-1 fingerprints for Google Sign-In configuration
# Make sure you have Java keytool in your PATH

echo "🔍 Getting SHA-1 Fingerprints for Google Sign-In..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if keytool is available
if ! command -v keytool &> /dev/null; then
    echo -e "${RED}❌ keytool not found. Please install Java JDK.${NC}"
    exit 1
fi

echo -e "${BLUE}📱 Android Keystore Fingerprints:${NC}"
echo ""

# Debug keystore (default Android debug keystore)
DEBUG_KEYSTORE="$HOME/.android/debug.keystore"
if [ -f "$DEBUG_KEYSTORE" ]; then
    echo -e "${GREEN}🔧 Debug Keystore:${NC}"
    echo "Location: $DEBUG_KEYSTORE"
    echo "SHA-1:"
    keytool -list -v -keystore "$DEBUG_KEYSTORE" -alias androiddebugkey -storepass android -keypass android | grep "SHA1:" | sed 's/.*SHA1: //'
    echo ""
else
    echo -e "${YELLOW}⚠️  Debug keystore not found at $DEBUG_KEYSTORE${NC}"
    echo ""
fi

# Release keystore (upload keystore)
RELEASE_KEYSTORE="android/upload-keystore.jks"
if [ -f "$RELEASE_KEYSTORE" ]; then
    echo -e "${GREEN}🚀 Release/Upload Keystore:${NC}"
    echo "Location: $RELEASE_KEYSTORE"
    echo "Alias: upload"
    echo "SHA-1:"
    
    # Read password from key.properties
    if [ -f "android/key.properties" ]; then
        STORE_PASSWORD=$(grep "storePassword=" android/key.properties | cut -d'=' -f2)
        KEY_PASSWORD=$(grep "keyPassword=" android/key.properties | cut -d'=' -f2)
        
        keytool -list -v -keystore "$RELEASE_KEYSTORE" -alias upload -storepass "$STORE_PASSWORD" -keypass "$KEY_PASSWORD" | grep "SHA1:" | sed 's/.*SHA1: //'
    else
        echo -e "${YELLOW}⚠️  key.properties not found. Please enter passwords manually:${NC}"
        keytool -list -v -keystore "$RELEASE_KEYSTORE" -alias upload | grep "SHA1:" | sed 's/.*SHA1: //'
    fi
    echo ""
else
    echo -e "${YELLOW}⚠️  Release keystore not found at $RELEASE_KEYSTORE${NC}"
    echo ""
fi

echo -e "${BLUE}📋 Expected SHA-1 Fingerprints (from google-services.json):${NC}"
echo ""

# Parse google-services.json to get expected fingerprints
if [ -f "android/app/google-services.json" ]; then
    echo -e "${GREEN}✅ From google-services.json:${NC}"
    
    # Extract SHA-1 hashes for com.busaty.school
    python3 -c "
import json
import sys

try:
    with open('android/app/google-services.json', 'r') as f:
        data = json.load(f)
    
    for client in data['client']:
        if client['client_info']['android_client_info']['package_name'] == 'com.busaty.school':
            oauth_clients = client.get('oauth_client', [])
            for oauth_client in oauth_clients:
                if oauth_client.get('client_type') == 1:  # Android client
                    android_info = oauth_client.get('android_info', {})
                    cert_hash = android_info.get('certificate_hash', '')
                    if cert_hash:
                        # Convert to uppercase with colons
                        formatted_hash = ':'.join(cert_hash[i:i+2].upper() for i in range(0, len(cert_hash), 2))
                        print(f'   • {formatted_hash}')
            break
except Exception as e:
    print(f'Error parsing google-services.json: {e}')
"
else
    echo -e "${RED}❌ google-services.json not found${NC}"
fi

echo ""
echo -e "${BLUE}🎯 Next Steps:${NC}"
echo "1. Compare the SHA-1 fingerprints above"
echo "2. Make sure all fingerprints are added to Firebase Console"
echo "3. For Play Store, get the SHA-1 from Google Play Console > App integrity > App signing"
echo "4. Download updated google-services.json after adding fingerprints"
echo ""
echo -e "${GREEN}✅ Fingerprint check completed!${NC}"
