import 'dart:convert';
import 'dart:io';

/// <PERSON>ript to verify the SHA-1 fix for Google Sign-In
/// Run with: dart run scripts/verify_sha1_fix.dart
void main() async {
  print('🔍 Verifying SHA-1 Fix for Google Sign-In...\n');

  final correctPlayStoreSHA1 = '3ceda50a3b31262b0dc1af26b85ec284da66b467';
  final correctPlayStoreSHA1Formatted =
      '3C:ED:A5:0A:3B:31:26:2B:0D:C1:AF:26:B8:5E:C2:84:DA:66:B4:67';

  print('🎯 Expected Play Store SHA-1: $correctPlayStoreSHA1Formatted');
  print('');

  await checkGoogleServicesJson(correctPlayStoreSHA1);
  await checkConfigFile(correctPlayStoreSHA1Formatted);

  print('\n📋 Next Steps if SHA-1 is missing:');
  print('1. Go to Firebase Console: https://console.firebase.google.com');
  print('2. Select project: test-5c820');
  print('3. Go to Project settings → General');
  print('4. Select Android app: com.busaty.school');
  print('5. Add SHA-1 fingerprint: $correctPlayStoreSHA1Formatted');
  print('6. Download new google-services.json');
  print('7. Replace android/app/google-services.json');
  print('8. Run: flutter clean && flutter pub get');
  print('9. Build: flutter build appbundle --release');
}

Future<void> checkGoogleServicesJson(String expectedHash) async {
  print('📱 Checking android/app/google-services.json...');

  final file = File('android/app/google-services.json');
  if (!file.existsSync()) {
    print('❌ google-services.json not found!');
    return;
  }

  try {
    final content = await file.readAsString();
    final json = jsonDecode(content);

    // Find com.busaty.school client
    final clients = json['client'] as List;
    final schoolClient = clients.firstWhere(
      (client) =>
          client['client_info']['android_client_info']['package_name'] ==
          'com.busaty.school',
      orElse: () => null,
    );

    if (schoolClient == null) {
      print('❌ com.busaty.school not found in google-services.json');
      return;
    }

    // Check OAuth clients for the correct SHA-1
    final oauthClients = schoolClient['oauth_client'] as List;
    final androidClients =
        oauthClients.where((client) => client['client_type'] == 1).toList();

    bool foundCorrectSHA1 = false;

    print('📋 Found ${androidClients.length} Android OAuth clients:');
    for (final client in androidClients) {
      final hash = client['android_info']['certificate_hash'];
      final clientId = client['client_id'];
      final formatted = formatSHA1(hash);

      if (hash.toLowerCase() == expectedHash.toLowerCase()) {
        foundCorrectSHA1 = true;
        print('   ✅ CORRECT: $formatted');
        print('     Client ID: $clientId');
      } else {
        print('   • $formatted');
        print('     Client ID: $clientId');
      }
    }

    print('');
    if (foundCorrectSHA1) {
      print('✅ Correct Play Store SHA-1 found in google-services.json!');
    } else {
      print('❌ Correct Play Store SHA-1 NOT found in google-services.json!');
      print(
          '   You need to add: ${formatSHA1(expectedHash)} to Firebase Console');
    }
  } catch (e) {
    print('❌ Error reading google-services.json: $e');
  }
}

Future<void> checkConfigFile(String expectedSHA1) async {
  print('\n📝 Checking lib/config/google_signin_config.dart...');

  final file = File('lib/config/google_signin_config.dart');
  if (!file.existsSync()) {
    print('❌ google_signin_config.dart not found!');
    return;
  }

  try {
    final content = await file.readAsString();

    if (content.contains(expectedSHA1)) {
      print('✅ Config file contains correct Play Store SHA-1');
    } else {
      print('⚠️  Config file may need updating with correct SHA-1');
    }
  } catch (e) {
    print('❌ Error reading config file: $e');
  }
}

String formatSHA1(String hash) {
  // Convert hash to uppercase with colons
  final upperHash = hash.toUpperCase();
  return List.generate((upperHash.length / 2).floor(),
      (i) => upperHash.substring(i * 2, i * 2 + 2)).join(':');
}
